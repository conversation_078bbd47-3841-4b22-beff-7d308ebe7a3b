"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Calendar,
  Clock,
  CreditCard,
  Package,
  User,
  MapPin,
  Phone,
  Mail,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Ban,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/product-utils";
import {
  calculateDaysRemaining,
  calculatePaymentProgress,
  formatLayBuyPrice,
  calculateRefundAmount,
} from "@/lib/lay-buy-utils";
import LayBuyPaymentForm from "./lay-buy-payment-form";
import LayBuyCancelRequest from "./lay-buy-cancel-request";

interface LayBuyOrderDetailProps {
  orderId: string;
}

interface LayBuyOrderData {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  upfrontAmount: number;
  remainingAmount: number;
  amountPaid: number;
  dueDate: string;
  gracePeriodEnd: string;
  shippingAddress: string;
  phoneNumber: string;
  notes?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  cancelledAt?: string;
  forfeitedAt?: string;
  refundAmount?: number;
  orderItems: Array<{
    id: string;
    productName: string;
    productBrand: string;
    productImages: string[];
    quantity: number;
    price: number;
    size?: string;
    color?: string;
  }>;
  payments: Array<{
    id: string;
    amount: number;
    paymentType: string;
    paymentMethod?: string;
    status: string;
    createdAt: string;
    verifiedAt?: string;
  }>;
}

export default function LayBuyOrderDetailContent({ orderId }: LayBuyOrderDetailProps) {
  const [order, setOrder] = useState<LayBuyOrderData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancellationRequest, setCancellationRequest] = useState<any>(null);

  const fetchOrderDetail = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch(`/api/lay-buy-orders/${orderId}`);
      const result = await response.json();

        if (result.success) {
          // Transform the data to match our interface
          const orderData = result.data;
          const formattedOrder: LayBuyOrderData = {
            id: orderData.id,
            orderNumber: orderData.orderNumber,
            status: orderData.status,
            totalAmount: orderData.totalAmount,
            upfrontAmount: orderData.upfrontAmount,
            remainingAmount: orderData.remainingAmount,
            amountPaid: orderData.amountPaid,
            dueDate: orderData.dueDate,
            gracePeriodEnd: orderData.gracePeriodEnd,
            shippingAddress: orderData.shippingAddress,
            phoneNumber: orderData.phoneNumber,
            notes: orderData.notes,
            adminNotes: orderData.adminNotes,
            createdAt: orderData.createdAt,
            updatedAt: orderData.updatedAt,
            completedAt: orderData.completedAt,
            cancelledAt: orderData.cancelledAt,
            forfeitedAt: orderData.forfeitedAt,
            refundAmount: orderData.refundAmount,
            orderItems: orderData.orderItems.map((item: any) => ({
              id: item.id,
              productName: item.product.name,
              productBrand: item.product.brand,
              productImages: item.product.images,
              quantity: item.quantity,
              price: item.price,
              size: item.size,
              color: item.color,
            })),
            payments: orderData.payments || [],
          };
          setOrder(formattedOrder);
          // Also fetch cancellation request if order exists
          fetchCancellationRequest();
        } else {
          setError(result.error || "Failed to load order details");
        }
      } catch (err) {
        console.error("Error fetching order details:", err);
        setError("Failed to load order details");
      } finally {
        setIsLoading(false);
      }
    };

    const fetchCancellationRequest = async () => {
      try {
        const response = await fetch(`/api/lay-buy-orders/${orderId}/cancel-request`);
        const result = await response.json();

        if (result.success && result.data) {
          setCancellationRequest(result.data);
        }
      } catch (error) {
        console.error("Error fetching cancellation request:", error);
        // Don't set error state for this as it's optional
      }
    };

  useEffect(() => {
    fetchOrderDetail();
  }, [orderId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="text-center py-12">
        <XCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
        <p className="text-gray-600 mb-6">{error || "The requested order could not be found."}</p>
        <Link href="/profile">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Profile
          </Button>
        </Link>
      </div>
    );
  }

  const dueDate = new Date(order.dueDate);
  const gracePeriodEnd = new Date(order.gracePeriodEnd);
  const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);
  const paymentProgress = calculatePaymentProgress(order.amountPaid, order.totalAmount);
  const remainingBalance = order.totalAmount - order.amountPaid;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { color: "bg-blue-100 text-blue-800", label: "Active" },
      COMPLETED: { color: "bg-green-100 text-green-800", label: "Completed" },
      CANCELLED: { color: "bg-gray-100 text-gray-800", label: "Cancelled" },
      FORFEITED: { color: "bg-red-100 text-red-800", label: "Forfeited" },
      REFUNDED: { color: "bg-purple-100 text-purple-800", label: "Refunded" },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getTimeRemainingBadge = () => {
    if (timeRemaining.status === 'forfeited') {
      return <Badge className="bg-red-100 text-red-800">Payment Period Expired</Badge>;
    } else if (timeRemaining.isInGracePeriod) {
      return <Badge className="bg-orange-100 text-orange-800">Grace Period - {timeRemaining.daysRemaining} days left</Badge>;
    } else if (timeRemaining.daysRemaining <= 7) {
      return <Badge className="bg-yellow-100 text-yellow-800">Due Soon - {timeRemaining.daysRemaining} days left</Badge>;
    } else {
      return <Badge className="bg-green-100 text-green-800">{timeRemaining.daysRemaining} days remaining</Badge>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/profile">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Lay-Buy Order Details</h1>
            <p className="text-gray-600">Order #{order.orderNumber}</p>
          </div>
        </div>
        {getStatusBadge(order.status)}
      </div>

      {/* Payment Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{paymentProgress.toFixed(1)}% Complete</span>
            </div>
            <Progress value={paymentProgress} className="h-3" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="font-semibold text-green-800">Paid</div>
              <div className="text-lg font-bold text-green-600">{formatLayBuyPrice(order.amountPaid)}</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="font-semibold text-orange-800">Remaining</div>
              <div className="text-lg font-bold text-orange-600">{formatLayBuyPrice(remainingBalance)}</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="font-semibold text-blue-800">Total</div>
              <div className="text-lg font-bold text-blue-600">{formatLayBuyPrice(order.totalAmount)}</div>
            </div>
          </div>

          {order.status === 'ACTIVE' && (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium">Time Remaining</span>
              </div>
              {getTimeRemainingBadge()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Form - Only show for active orders */}
      {order.status === 'ACTIVE' && remainingBalance > 0 && (
        <LayBuyPaymentForm
          orderId={order.id}
          orderNumber={order.orderNumber}
          remainingBalance={remainingBalance}
          onPaymentAdded={fetchOrderDetail}
        />
      )}

      {/* Cancel Request - Only show for active orders */}
      {order.status === 'ACTIVE' && !cancellationRequest && (
        <LayBuyCancelRequest
          orderId={order.id}
          orderNumber={order.orderNumber}
          amountPaid={order.amountPaid}
          dueDate={order.dueDate}
          onCancelRequested={fetchOrderDetail}
        />
      )}

      {/* Cancellation Request Status */}
      {cancellationRequest && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5" />
              Cancellation Request
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Status:</span>
              {cancellationRequest.status === 'PENDING' && (
                <Badge className="bg-yellow-100 text-yellow-800">Pending Review</Badge>
              )}
              {cancellationRequest.status === 'APPROVED' && (
                <Badge className="bg-green-100 text-green-800">Approved</Badge>
              )}
              {cancellationRequest.status === 'REJECTED' && (
                <Badge className="bg-red-100 text-red-800">Rejected</Badge>
              )}
              {cancellationRequest.status === 'PROCESSED' && (
                <Badge className="bg-blue-100 text-blue-800">Processed</Badge>
              )}
            </div>

            {cancellationRequest.refundAmount && (
              <div className="flex items-center justify-between">
                <span className="font-medium">Refund Amount:</span>
                <span className="text-lg font-semibold text-green-600">
                  {formatLayBuyPrice(cancellationRequest.refundAmount)}
                </span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <span className="font-medium">Requested:</span>
              <span>{new Date(cancellationRequest.createdAt).toLocaleDateString()}</span>
            </div>

            {cancellationRequest.reason && (
              <div>
                <span className="font-medium">Reason:</span>
                <p className="text-gray-700 mt-1">{cancellationRequest.reason}</p>
              </div>
            )}

            {cancellationRequest.adminNotes && (
              <div>
                <span className="font-medium">Admin Notes:</span>
                <p className="text-gray-700 mt-1">{cancellationRequest.adminNotes}</p>
              </div>
            )}

            {cancellationRequest.status === 'PENDING' && (
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  Your cancellation request is being reviewed. We'll contact you within 24 hours with an update.
                </AlertDescription>
              </Alert>
            )}

            {cancellationRequest.status === 'APPROVED' && order.status === 'CANCELLED' && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Your cancellation has been approved. The refund will be processed within 3-5 business days.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Cancelled Order Information */}
      {order.status === 'CANCELLED' && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <XCircle className="h-5 w-5" />
              Order Cancelled
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {order.cancelledAt && (
              <div className="flex items-center justify-between">
                <span className="font-medium">Cancelled Date:</span>
                <span>{new Date(order.cancelledAt).toLocaleDateString()}</span>
              </div>
            )}

            {order.refundAmount && (
              <div className="flex items-center justify-between">
                <span className="font-medium">Refund Amount:</span>
                <span className="text-lg font-semibold text-green-600">
                  {formatLayBuyPrice(order.refundAmount)}
                </span>
              </div>
            )}

            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                This order has been cancelled. Product stock has been restored and any applicable refund will be processed.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Order Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Order Items
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {order.orderItems.map((item) => (
              <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                  {item.productImages.length > 0 ? (
                    <img
                      src={item.productImages[0]}
                      alt={item.productName}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">{item.productName}</h3>
                  <p className="text-sm text-gray-600">{item.productBrand}</p>
                  {(item.size || item.color) && (
                    <p className="text-xs text-gray-500">
                      {item.size && `Size: ${item.size}`}
                      {item.size && item.color && " • "}
                      {item.color && `Color: ${item.color}`}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <div className="font-semibold">{formatPrice(item.price)}</div>
                  <div className="text-sm text-gray-600">Qty: {item.quantity}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Shipping Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Shipping Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-start gap-3">
            <MapPin className="h-4 w-4 text-gray-500 mt-1" />
            <div>
              <p className="font-medium">Delivery Address</p>
              <p className="text-sm text-gray-600">{order.shippingAddress}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Phone className="h-4 w-4 text-gray-500" />
            <div>
              <p className="font-medium">Phone Number</p>
              <p className="text-sm text-gray-600">{order.phoneNumber}</p>
            </div>
          </div>
          {order.notes && (
            <div className="flex items-start gap-3">
              <Mail className="h-4 w-4 text-gray-500 mt-1" />
              <div>
                <p className="font-medium">Order Notes</p>
                <p className="text-sm text-gray-600">{order.notes}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment History */}
      {order.payments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {order.payments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      payment.status === 'VERIFIED' ? 'bg-green-500' :
                      payment.status === 'REJECTED' ? 'bg-red-500' : 'bg-yellow-500'
                    }`} />
                    <div>
                      <p className="font-medium">{formatLayBuyPrice(payment.amount)}</p>
                      <p className="text-xs text-gray-500">
                        {payment.paymentType} • {new Date(payment.createdAt).toLocaleDateString()}
                        {payment.paymentMethod && ` • ${payment.paymentMethod}`}
                      </p>
                    </div>
                  </div>
                  <Badge className={
                    payment.status === 'VERIFIED' ? 'bg-green-100 text-green-800' :
                    payment.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }>
                    {payment.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Important Dates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Important Dates
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Order Created</span>
            <span className="text-sm font-medium">{new Date(order.createdAt).toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Payment Due Date</span>
            <span className="text-sm font-medium">{dueDate.toLocaleDateString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600">Grace Period Ends</span>
            <span className="text-sm font-medium">{gracePeriodEnd.toLocaleDateString()}</span>
          </div>
          {order.completedAt && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Completed</span>
              <span className="text-sm font-medium text-green-600">{new Date(order.completedAt).toLocaleDateString()}</span>
            </div>
          )}
          {order.cancelledAt && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Cancelled</span>
              <span className="text-sm font-medium text-red-600">{new Date(order.cancelledAt).toLocaleDateString()}</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
