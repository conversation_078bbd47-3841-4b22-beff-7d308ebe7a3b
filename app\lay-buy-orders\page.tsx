"use client";

import { useSession } from "@/lib/auth-client";
import { UserRoute } from "@/components/auth/protected-route";
import NavBar from "@/components/navbar";
import LayBuyOrdersList from "@/components/lay-buy/lay-buy-orders-list";
import { User } from "@/utils/types";
import { useEffect, useState } from "react";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import NavBarSkeleton from "@/components/navBarSkeleton";

export default function LayBuyOrdersPage() {
  return (
    <UserRoute>
      <LayBuyOrdersPageContent />
    </UserRoute>
  );
}

function LayBuyOrdersPageContent() {
  const { data: session, error } = useSession();
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // At this point, we know the user is authenticated due to UserRoute
  const user = session!.user;

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        try {
          const userResponse = await getUserById(user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    getUserDetails();
  }, [user]);

  if (isLoading || !userWithRole) {
    if (error) {
      return (
        <div className="w-full h-screen flex items-center justify-center">
          <p>{error.message}</p>
        </div>
      );
    }
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {isLoading ? (
        <NavBarSkeleton loading={isLoading} user={null} />
      ) : (
        <NavBar user={userWithRole} />
      )}
      <div className="container mx-auto px-4 py-6">
        <LayBuyOrdersList />
      </div>
    </div>
  );
}
