/**
 * Lay-Buy Payment Management Actions
 * Server actions for managing payments, verifications, and balance updates
 */

import prisma from "@/lib/prisma";
import { calculateRefundAmount } from "@/lib/lay-buy-utils";

/**
 * Add a new payment to a Lay-Buy order
 */
export async function addLayBuyPayment(paymentData: {
  layBuyOrderId: string;
  amount: number;
  paymentType: "UPFRONT" | "INSTALLMENT" | "COMPLETION";
  paymentMethod?: string;
  paymentProof?: string;
  notes?: string;
  userId?: string; // For customer-initiated payments
}) {
  try {
    // Verify the order exists and get current details
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: paymentData.layBuyOrderId },
      include: {
        user: true,
        payments: true,
      },
    });

    if (!order) {
      return { success: false, error: "Lay-Buy order not found" };
    }

    // Check if order is still active
    if (order.status !== "ACTIVE") {
      return { success: false, error: "Cannot add payments to inactive orders" };
    }

    // Validate payment amount
    const remainingBalance = order.totalAmount - order.amountPaid;
    if (paymentData.amount <= 0) {
      return { success: false, error: "Payment amount must be greater than zero" };
    }

    if (paymentData.amount > remainingBalance) {
      return { success: false, error: `Payment amount exceeds remaining balance of M${remainingBalance.toFixed(2)}` };
    }

    // Create the payment record
    const payment = await prisma.layBuyPayment.create({
      data: {
        layBuyOrderId: paymentData.layBuyOrderId,
        amount: paymentData.amount,
        paymentType: paymentData.paymentType,
        paymentMethod: paymentData.paymentMethod,
        paymentProof: paymentData.paymentProof,
        notes: paymentData.notes,
        status: "PENDING", // All payments start as pending verification
      },
    });

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error adding Lay-Buy payment:", error);
    return { success: false, error: "Failed to add payment" };
  }
}

/**
 * Verify a payment and update order balance
 */
export async function verifyLayBuyPayment(
  paymentId: string,
  verifiedBy: string,
  status: "VERIFIED" | "REJECTED",
  adminNotes?: string
) {
  try {
    const payment = await prisma.layBuyPayment.findUnique({
      where: { id: paymentId },
      include: {
        layBuyOrder: {
          include: {
            user: true,
            payments: {
              where: { status: "VERIFIED" },
            },
          },
        },
      },
    });

    if (!payment) {
      return { success: false, error: "Payment not found" };
    }

    if (payment.status !== "PENDING") {
      return { success: false, error: "Payment has already been processed" };
    }

    const result = await prisma.$transaction(async (tx) => {
      // Update payment status
      const updatedPayment = await tx.layBuyPayment.update({
        where: { id: paymentId },
        data: {
          status,
          verifiedBy,
          verifiedAt: new Date(),
          notes: adminNotes ? `${payment.notes || ""}\nAdmin: ${adminNotes}`.trim() : payment.notes,
        },
      });

      if (status === "VERIFIED") {
        // Update order balance
        const newAmountPaid = payment.layBuyOrder.amountPaid + payment.amount;
        const isCompleted = newAmountPaid >= payment.layBuyOrder.totalAmount;

        const updatedOrder = await tx.layBuyOrder.update({
          where: { id: payment.layBuyOrderId },
          data: {
            amountPaid: newAmountPaid,
            status: isCompleted ? "COMPLETED" : "ACTIVE",
            completedAt: isCompleted ? new Date() : null,
            updatedAt: new Date(),
          },
        });

        return { payment: updatedPayment, order: updatedOrder, completed: isCompleted };
      }

      return { payment: updatedPayment, order: payment.layBuyOrder, completed: false };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error verifying Lay-Buy payment:", error);
    return { success: false, error: "Failed to verify payment" };
  }
}

/**
 * Cancel a Lay-Buy order and process refund
 */
export async function cancelLayBuyOrder(
  orderId: string,
  cancelledBy: string,
  reason?: string,
  adminNotes?: string
) {
  try {
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        payments: {
          where: { status: "VERIFIED" },
        },
      },
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    if (order.status !== "ACTIVE") {
      return { success: false, error: "Can only cancel active orders" };
    }

    // Calculate refund amount based on timing
    const dueDate = new Date(order.dueDate);
    const refundInfo = calculateRefundAmount(order.amountPaid, dueDate);

    const result = await prisma.$transaction(async (tx) => {
      // Update order status
      const updatedOrder = await tx.layBuyOrder.update({
        where: { id: orderId },
        data: {
          status: "CANCELLED",
          cancelledAt: new Date(),
          refundAmount: refundInfo.refundAmount,
          adminNotes: adminNotes ? `${order.adminNotes || ""}\nCancellation: ${adminNotes}`.trim() : order.adminNotes,
          updatedAt: new Date(),
        },
      });

      // Restore product stock
      const stockUpdates = await Promise.all(
        order.orderItems?.map((item: any) =>
          tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          })
        ) || []
      );

      return { order: updatedOrder, refundInfo, stockUpdates };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error cancelling Lay-Buy order:", error);
    return { success: false, error: "Failed to cancel order" };
  }
}

/**
 * Forfeit a Lay-Buy order (no refund)
 */
export async function forfeitLayBuyOrder(
  orderId: string,
  forfeitedBy: string,
  reason?: string
) {
  try {
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      include: {
        orderItems: true,
      },
    });

    if (!order) {
      return { success: false, error: "Order not found" };
    }

    if (order.status !== "ACTIVE") {
      return { success: false, error: "Can only forfeit active orders" };
    }

    const result = await prisma.$transaction(async (tx) => {
      // Update order status
      const updatedOrder = await tx.layBuyOrder.update({
        where: { id: orderId },
        data: {
          status: "FORFEITED",
          forfeitedAt: new Date(),
          refundAmount: 0, // No refund for forfeited orders
          adminNotes: reason ? `${order.adminNotes || ""}\nForfeited: ${reason}`.trim() : order.adminNotes,
          updatedAt: new Date(),
        },
      });

      // Restore product stock
      const stockUpdates = await Promise.all(
        order.orderItems.map((item) =>
          tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          })
        )
      );

      return { order: updatedOrder, stockUpdates };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error forfeiting Lay-Buy order:", error);
    return { success: false, error: "Failed to forfeit order" };
  }
}

/**
 * Get payment history for an order
 */
export async function getLayBuyPaymentHistory(orderId: string) {
  try {
    const payments = await prisma.layBuyPayment.findMany({
      where: { layBuyOrderId: orderId },
      orderBy: { createdAt: "desc" },
    });

    return { success: true, data: payments };
  } catch (error) {
    console.error("Error fetching payment history:", error);
    return { success: false, error: "Failed to fetch payment history" };
  }
}

/**
 * Get pending payments for admin review
 */
export async function getPendingPayments(filters?: {
  page?: number;
  limit?: number;
}) {
  try {
    const { page = 1, limit = 20 } = filters || {};
    const skip = (page - 1) * limit;

    const [payments, total] = await Promise.all([
      prisma.layBuyPayment.findMany({
        where: { status: "PENDING" },
        include: {
          layBuyOrder: {
            include: {
              user: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.layBuyPayment.count({
        where: { status: "PENDING" },
      }),
    ]);

    return {
      success: true,
      data: {
        payments,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  } catch (error) {
    console.error("Error fetching pending payments:", error);
    return { success: false, error: "Failed to fetch pending payments" };
  }
}
