import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { cancelLayBuyOrder } from "@/actions/layBuyPaymentActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/admin/lay-buy-orders/[id]/cancel - Cancel a Lay-Buy order
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { reason, adminNotes } = body;

    const result = await cancelLayBuyOrder(paramsId, user.id, reason, adminNotes);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    const response: ApiResponse<typeof result.data> = {
      success: true,
      data: result.data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error cancelling Lay-Buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to cancel order" },
      { status: 500 }
    );
  }
}
