-- CreateTable
CREATE TABLE "notice_read" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "noticeId" TEXT NOT NULL,
    "readAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notice_read_pkey" PRIMARY KEY ("id")
);

-- AlterEnum
ALTER TYPE "MessageStatus" ADD VALUE 'RESOLVED';
ALTER TYPE "MessageStatus" DROP VALUE 'ARCHIVED';

-- CreateIndex
CREATE UNIQUE INDEX "notice_read_userId_noticeId_key" ON "notice_read"("userId", "noticeId");

-- AddForeignKey
ALTER TABLE "notice_read" ADD CONSTRAINT "notice_read_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "notice_read" ADD CONSTRAINT "notice_read_noticeId_fkey" FOREIGN KEY ("noticeId") REFERENCES "notice"("id") ON DELETE CASCADE ON UPDATE CASCADE;
