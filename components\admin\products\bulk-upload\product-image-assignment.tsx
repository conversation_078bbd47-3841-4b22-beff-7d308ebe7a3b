"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  X, 
  Eye, 
  ArrowUp, 
  ArrowDown,
  Image as ImageIcon
} from "lucide-react";
import { UploadedImage } from "./bulk-upload-content";

interface ProductImageAssignmentProps {
  uploadedImages: UploadedImage[];
  assignedImages: string[];
  onImagesAssigned: (images: string[]) => void;
}

export default function ProductImageAssignment({
  uploadedImages,
  assignedImages,
  onImagesAssigned
}: ProductImageAssignmentProps) {
  const [showImagePicker, setShowImagePicker] = useState(false);

  const availableImages = uploadedImages.filter(img => !assignedImages.includes(img.url));
  const assignedImageObjects = assignedImages.map(url => 
    uploadedImages.find(img => img.url === url)
  ).filter(Boolean) as UploadedImage[];

  const addImage = (imageUrl: string) => {
    if (!assignedImages.includes(imageUrl)) {
      onImagesAssigned([...assignedImages, imageUrl]);
    }
  };

  const removeImage = (imageUrl: string) => {
    onImagesAssigned(assignedImages.filter(url => url !== imageUrl));
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...assignedImages];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onImagesAssigned(newImages);
  };

  const moveImageUp = (index: number) => {
    if (index > 0) {
      moveImage(index, index - 1);
    }
  };

  const moveImageDown = (index: number) => {
    if (index < assignedImages.length - 1) {
      moveImage(index, index + 1);
    }
  };

  return (
    <div className="space-y-4">
      {/* Assigned Images */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-900">
            Assigned Images ({assignedImages.length})
          </h4>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowImagePicker(!showImagePicker)}
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Images
          </Button>
        </div>

        {assignedImageObjects.length > 0 ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {assignedImageObjects.map((image, index) => (
              <Card key={image.id} className="relative group">
                <CardContent className="p-2">
                  <div className="aspect-square relative overflow-hidden rounded-lg bg-gray-100">
                    <img
                      src={image.url}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />
                    
                    {/* Primary Badge */}
                    {index === 0 && (
                      <Badge 
                        variant="default" 
                        className="absolute top-1 left-1 text-xs bg-blue-600"
                      >
                        Primary
                      </Badge>
                    )}

                    {/* Controls */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div className="flex space-x-1">
                        <Button
                          variant="secondary"
                          size="sm"
                          className="w-8 h-8 p-0"
                          onClick={() => moveImageUp(index)}
                          disabled={index === 0}
                        >
                          <ArrowUp className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          className="w-8 h-8 p-0"
                          onClick={() => moveImageDown(index)}
                          disabled={index === assignedImages.length - 1}
                        >
                          <ArrowDown className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          className="w-8 h-8 p-0"
                          onClick={() => removeImage(image.url)}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-600 mt-1 truncate">
                    {image.name}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">No images assigned</p>
            <p className="text-sm text-gray-400">Click "Add Images" to assign images to this product</p>
          </div>
        )}
      </div>

      {/* Image Picker */}
      {showImagePicker && (
        <Card className="border-2 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">
                Available Images ({availableImages.length})
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowImagePicker(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {availableImages.length > 0 ? (
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2 max-h-64 overflow-y-auto">
                {availableImages.map((image) => (
                  <Card 
                    key={image.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => addImage(image.url)}
                  >
                    <CardContent className="p-1">
                      <div className="aspect-square relative overflow-hidden rounded bg-gray-100">
                        <img
                          src={image.url}
                          alt={image.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all flex items-center justify-center">
                          <Plus className="w-6 h-6 text-white opacity-0 hover:opacity-100 transition-opacity" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No available images</p>
                <p className="text-sm text-gray-400">All uploaded images have been assigned</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <div className="text-sm text-gray-500 space-y-1">
        <p>• The first image will be used as the primary product image</p>
        <p>• Use the arrow buttons to reorder images</p>
        <p>• Click "Add Images" to assign more images from your uploads</p>
      </div>
    </div>
  );
}
