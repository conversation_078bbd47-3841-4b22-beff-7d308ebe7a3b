"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  CreditCard,
  Search,
  Filter,
  Calendar,
  User,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { formatPrice } from "@/lib/product-utils";
import { calculateDaysRemaining, formatLayBuyPrice } from "@/lib/lay-buy-utils";
import LayBuyExport from "./lay-buy-export";

interface LayBuyOrderSummary {
  id: string;
  orderNumber: string;
  status: string;
  totalAmount: number;
  amountPaid: number;
  remainingAmount: number;
  dueDate: string;
  gracePeriodEnd: string;
  createdAt: string;
  customer: {
    id: string;
    name: string;
    email: string;
  };
  itemCount: number;
  paymentCount: number;
  reminderCount: number;
}

export default function AdminLayBuyOrdersContent() {
  const [orders, setOrders] = useState<LayBuyOrderSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    overdue: 0,
    completed: 0,
    totalValue: 0,
    outstandingBalance: 0,
  });

  useEffect(() => {
    fetchLayBuyOrders();
  }, [statusFilter, searchTerm]);

  const fetchLayBuyOrders = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams();
      if (statusFilter !== "ALL") params.append("status", statusFilter);
      if (searchTerm) params.append("search", searchTerm);

      const response = await fetch(`/api/admin/lay-buy-orders?${params}`);
      const result = await response.json();

      if (result.success) {
        setOrders(result.data.orders);
        calculateStats(result.data.orders);
      }
    } catch (error) {
      console.error("Error fetching Lay-Buy orders:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStats = (orderList: LayBuyOrderSummary[]) => {
    const stats = {
      total: orderList.length,
      active: orderList.filter(o => o.status === 'ACTIVE').length,
      overdue: 0,
      completed: orderList.filter(o => o.status === 'COMPLETED').length,
      totalValue: orderList.reduce((sum, o) => sum + o.totalAmount, 0),
      outstandingBalance: orderList.reduce((sum, o) => sum + (o.totalAmount - o.amountPaid), 0),
    };

    // Calculate overdue orders
    stats.overdue = orderList.filter(order => {
      if (order.status !== 'ACTIVE') return false;
      const dueDate = new Date(order.dueDate);
      const gracePeriodEnd = new Date(order.gracePeriodEnd);
      const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);
      return timeRemaining.isOverdue;
    }).length;

    setStats(stats);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { color: "bg-blue-100 text-blue-800", label: "Active" },
      COMPLETED: { color: "bg-green-100 text-green-800", label: "Completed" },
      CANCELLED: { color: "bg-gray-100 text-gray-800", label: "Cancelled" },
      FORFEITED: { color: "bg-red-100 text-red-800", label: "Forfeited" },
      REFUNDED: { color: "bg-purple-100 text-purple-800", label: "Refunded" },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getUrgencyBadge = (order: LayBuyOrderSummary) => {
    if (order.status !== 'ACTIVE') return null;
    
    const dueDate = new Date(order.dueDate);
    const gracePeriodEnd = new Date(order.gracePeriodEnd);
    const timeRemaining = calculateDaysRemaining(dueDate, gracePeriodEnd);

    if (timeRemaining.status === 'forfeited') {
      return <Badge className="bg-red-100 text-red-800">Forfeited</Badge>;
    } else if (timeRemaining.isInGracePeriod) {
      return <Badge className="bg-orange-100 text-orange-800">Grace Period</Badge>;
    } else if (timeRemaining.daysRemaining <= 3) {
      return <Badge className="bg-yellow-100 text-yellow-800">Due Soon</Badge>;
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Lay-Buy Orders</h1>
          <p className="text-gray-600">Manage and track all Lay-Buy orders</p>
        </div>
        <Button onClick={fetchLayBuyOrders} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <CreditCard className="h-6 w-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Orders</p>
                <p className="text-2xl font-bold text-blue-600">{stats.active}</p>
              </div>
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overdue Orders</p>
                <p className="text-2xl font-bold text-orange-600">{stats.overdue}</p>
              </div>
              <AlertTriangle className="h-6 w-6 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Outstanding Balance</p>
                <p className="text-2xl font-bold text-red-600">{formatLayBuyPrice(stats.outstandingBalance)}</p>
              </div>
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by order number, customer name, or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="FORFEITED">Forfeited</SelectItem>
                <SelectItem value="REFUNDED">Refunded</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      <Card>
        <CardHeader>
          <CardTitle>Orders</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Lay-Buy Orders Found</h3>
              <p className="text-gray-600">No orders match your current filters.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CreditCard className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{order.orderNumber}</h3>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <User className="h-4 w-4" />
                        <span>{order.customer.name}</span>
                        <span>•</span>
                        <span>{order.customer.email}</span>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusBadge(order.status)}
                        {getUrgencyBadge(order)}
                        <span className="text-xs text-gray-500">
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {new Date(order.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-semibold text-lg">{formatLayBuyPrice(order.totalAmount)}</div>
                    <div className="text-sm text-gray-600">
                      Paid: {formatLayBuyPrice(order.amountPaid)}
                    </div>
                    <div className="text-sm text-orange-600">
                      Remaining: {formatLayBuyPrice(order.totalAmount - order.amountPaid)}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/admin/lay-buy-orders/${order.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Export Data */}
      <LayBuyExport />
    </div>
  );
}
