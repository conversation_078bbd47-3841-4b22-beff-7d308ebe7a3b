import { ShoeType } from "@/utils/types";
import React from "react";
import { Card, CardContent, CardHeader } from "./ui/card";
import Link from "next/link";
import { Button } from "./ui/button";

type Props = {
  shoes: ShoeType[] | [];
};

const ShoeCards = ({ shoes }: Props) => {
  if (!shoes.length) {
    return (
      <div className="w-full h-full text-muted-foreground justify-center flex gap-2">
        <i>No shoes to show right now</i> <span className="text-xl">😔</span>
      </div>
    );
  }

  return (
    <>
      {shoes.map((shoe, idx) => (
        <Card key={idx} className="w-full md:w-72 min-h-[368px]">
          <CardHeader className="p-0">
            <div
              className="flex items-center justify-center h-40 w-[90%] mx-auto rounded-md"
              style={{
                backgroundImage: `url(${shoe.image})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            ></div>
          </CardHeader>
          <CardContent className="flex flex-col gap-2">
            <h3 className="font-medium text-base text-gray-900">{shoe.name}</h3>
            <div className="flex items-center gap-1">
              {[...Array(Number(shoe.rating.toPrecision(1)))].map((_, i) => (
                <svg
                  key={i}
                  className="w-4 h-4 text-yellow-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.175c.969 0 1.371 1.24.588 1.81l-3.38 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.38-2.454a1 1 0 00-1.175 0l-3.38 2.454c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.05 9.394c-.783-.57-.38-1.81.588-1.81h4.175a1 1 0 00.95-.69l1.286-3.967z" />
                </svg>
              ))}
              <span className="text-md text-muted-foreground">
                ({shoe.numberOfRatings})
              </span>
            </div>
            <div className="flex items-center justify-between">
              {shoe.discount ? (
                <>
                  {" "}
                  <span className="text-gray-900 font-semibold text-lg">
                    M {shoe.discountedPrice.toFixed(2)}
                  </span>
                  <span className="text-gray-400 line-through text-sm">
                    M {shoe.price.toFixed(2)}
                  </span>
                </>
              ) : (
                <div className="pt-5 flex justify-between items-center w-full">
                  <Link href={`shoe-details/${shoe.id}`}>
                    <Button className="">View details</Button>
                  </Link>
                  <span className="text-gray-900 font-semibold text-lg">
                    M {shoe.price.toFixed(2)}
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center justify-between">
              {shoe.discount && (
                <>
                  <Link href={`shoe-details/${shoe.id}`}>
                    <Button className="">View details</Button>
                  </Link>
                  <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-semibold">
                    {Math.round(
                      ((shoe.discountedPrice - shoe.price) /
                        shoe.discountedPrice) *
                        100
                    )}
                    % OFF
                  </span>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  );
};

export default ShoeCards;
