import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";


// GET /api/orders/[id] - Get single order
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const order = await prisma.order.findFirst({
      where: {
        id: paramsId,
        userId: user.id,
      },
      include: {
        orderItems: {
          include: {
            product: {
              include: {
                category: true,
              },
            },
          },
        },
        discountCode: true,
        paymentProof: true,
      },
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    const response: ApiResponse<typeof order> = {
      success: true,
      data: order,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch order" },
      { status: 500 }
    );
  }
}
