import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bell } from "lucide-react";

// Temporary mock data for testing
const mockNotices = [
  { id: "1", title: "Welcome!", content: "Thanks for joining our platform.", isRead: false, createdAt: new Date() },
  { id: "2", title: "New Features", content: "We've added new features to improve your experience.", isRead: true, createdAt: new Date() },
];

export default function NoticeButton() {
  const [open, setOpen] = useState(false);
  const [notices, setNotices] = useState<any[]>(mockNotices);
  const [loading, setLoading] = useState(false);

  // Temporarily disabled API call - uncomment after database migration
  /*
  useEffect(() => {
    if (open) {
      setLoading(true);
      fetch("/api/notices")
        .then(res => {
          if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
          }
          return res.json();
        })
        .then(data => {
          if (data.success) {
            setNotices(data.data);
          } else {
            console.error("API error:", data.error);
            setNotices([]);
          }
        })
        .catch(error => {
          console.error("Error fetching notices:", error);
          setNotices([]);
        })
        .finally(() => setLoading(false));
    }
  }, [open]);
  */

  const unreadCount = notices.filter(n => !n.isRead).length;

  // Mark as read when a notice is clicked
  const markAsRead = async (noticeId: string) => {
    // Temporarily just update local state
    setNotices(notices => notices.map(n => n.id === noticeId ? { ...n, isRead: true } : n));
    
    // Uncomment after database migration
    /*
    await fetch("/api/notices", {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ noticeId }),
    });
    setNotices(notices => notices.map(n => n.id === noticeId ? { ...n, isRead: true } : n));
    */
  };

  return (
    <>
      <Button
        variant="outline"
        className="relative bg-green-600 hover:bg-green-700 text-white"
        onClick={() => setOpen(true)}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-2 -right-2 bg-red-600 text-white rounded-full px-2 py-0.5 text-xs">{unreadCount}</Badge>
        )}
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Notices</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            {loading && <div className="text-gray-500">Loading...</div>}
            {!loading && notices.length === 0 && <div className="text-gray-500">No notices.</div>}
            {notices.map(notice => (
              <div
                key={notice.id}
                className={`p-3 rounded border cursor-pointer ${notice.isRead ? 'bg-white' : 'bg-green-100 border-green-400'}`}
                onClick={() => !notice.isRead && markAsRead(notice.id)}
              >
                <div className="font-semibold">{notice.title}</div>
                <div className="text-sm text-gray-700">{notice.content}</div>
                <div className="text-xs text-gray-400 mt-1">{new Date(notice.createdAt).toLocaleString()}</div>
                {!notice.isRead && <span className="text-xs text-green-700 font-semibold">New</span>}
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
} 