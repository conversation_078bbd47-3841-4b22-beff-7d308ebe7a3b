import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { getCurrentUser } from "@/lib/auth-utils";

const f = createUploadthing();

// FileRouter for your app, can contain multiple FileRoutes
export const ourFileRouter = {
  // Product image uploader - Admin only
  productImageUploader: f({ image: { maxFileSize: "4MB", maxFileCount: 5 } })
    .middleware(async ({ req }) => {
      // Get session
      const user = await getCurrentUser();

      // Check if user is authenticated and is admin
      if (!user || user.role !== "ADMIN") {
        throw new UploadThingError("Unauthorized - Admin access required");
      }

      // Return metadata to be stored with the file
      return {
        userId: user.id,
        userRole: user.role,
        uploadType: "product",
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Product image upload complete for userId:", metadata.userId);
      console.log("File URL:", file.ufsUrl);

      // Return data to the client
      return {
        uploadedBy: metadata.userId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
      };
    }),

  // Bulk product image uploader - Admin only
  bulkProductImageUploader: f({
    image: { maxFileSize: "4MB", maxFileCount: 100 },
  })
    .middleware(async ({ req }) => {
      // Get session
      const user = await getCurrentUser();

      // Check if user is authenticated and is admin
      if (!user || user.role !== "ADMIN") {
        throw new UploadThingError("Unauthorized - Admin access required");
      }

      // Return metadata to be stored with the file
      return {
        userId: user.id,
        userRole: user.role,
        uploadType: "bulk_product",
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log(
        "Bulk product image upload complete for userId:",
        metadata.userId
      );
      console.log("File URL:", file.ufsUrl);

      // Return data to the client
      return {
        uploadedBy: metadata.userId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
      };
    }),

  // Payment proof uploader - Authenticated users only
  paymentProofUploader: f({ image: { maxFileSize: "8MB", maxFileCount: 1 } })
    .middleware(async ({ req }) => {
      // Get session
      const user = await getCurrentUser();

      // Check if user is authenticated
      if (!user) {
        throw new UploadThingError("Unauthorized - Authentication required");
      }

      // Return metadata to be stored with the file
      return {
        userId: user.id,
        uploadType: "payment_proof",
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("Payment proof upload complete for userId:", metadata.userId);
      console.log("File URL:", file.ufsUrl);

      // Return data to the client
      return {
        uploadedBy: metadata.userId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
      };
    }),

  // Testimonial image uploader - Admin only
  testimonialImageUploader: f({
    image: { maxFileSize: "2MB", maxFileCount: 1 },
  })
    .middleware(async ({ req }) => {
      // Get session
      const user = await getCurrentUser();

      // Check if user is authenticated and is admin
      if (!user || user.role !== "ADMIN") {
        throw new UploadThingError("Unauthorized - Admin access required");
      }

      // Return metadata to be stored with the file
      return {
        userId: user.id,
        userRole: user.role,
        uploadType: "testimonial",
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log(
        "Testimonial image upload complete for userId:",
        metadata.userId
      );
      console.log("File URL:", file.ufsUrl);

      // Return data to the client
      return {
        uploadedBy: metadata.userId,
        url: file.ufsUrl,
        name: file.name,
        size: file.size,
      };
    }),
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;
