"use client";

import { useState } from "react";
import { CheckoutData } from "./checkout-content";
import { useCart } from "@/contexts/cart-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tag, X, Check, MessageCircle } from "lucide-react";
import { formatPrice, getEffectivePrice, calculateDeliveryFee } from "@/lib/product-utils";

interface CheckoutOrderSummaryProps {
  checkoutData: CheckoutData;
  onUpdate: (data: Partial<CheckoutData>) => void;
  currentStep: number;
}

export default function CheckoutOrderSummary({ 
  checkoutData, 
  onUpdate, 
  currentStep 
}: CheckoutOrderSummaryProps) {
  const [discountCode, setDiscountCode] = useState(checkoutData.discountCode || "");
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false);
  const [discountError, setDiscountError] = useState("");

  const { state: cartState } = useCart();

  const subtotal = cartState.totalPrice;
  const discountAmount = checkoutData.discountAmount;

  // Calculate delivery fee based on district and order amount
  const deliveryInfo = calculateDeliveryFee(checkoutData.shipping.district, subtotal - discountAmount);
  const deliveryCost = deliveryInfo.fee;
  const total = subtotal - discountAmount + deliveryCost;

  const applyDiscountCode = async () => {
    if (!discountCode.trim()) return;

    setIsApplyingDiscount(true);
    setDiscountError("");

    try {
      const response = await fetch("/api/discount-codes/validate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: discountCode,
          orderAmount: subtotal,
        }),
      });

      const result = await response.json();

      if (result.success) {
        const discount = result.data;
        let discountAmount = 0;

        if (discount.type === "PERCENTAGE") {
          discountAmount = (subtotal * discount.value) / 100;
        } else {
          discountAmount = discount.value;
        }

        // Ensure discount doesn't exceed order total
        discountAmount = Math.min(discountAmount, subtotal);

        onUpdate({
          discountCode: discountCode,
          discountAmount: discountAmount,
        });
      } else {
        setDiscountError(result.error || "Invalid discount code");
      }
    } catch (error) {
      setDiscountError("Failed to apply discount code");
    } finally {
      setIsApplyingDiscount(false);
    }
  };

  const removeDiscountCode = () => {
    setDiscountCode("");
    setDiscountError("");
    onUpdate({
      discountCode: undefined,
      discountAmount: 0,
    });
  };

  return (
    <div className="space-y-6">
      {/* Order Items */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {cartState.items.map((item) => {
              const effectivePrice = getEffectivePrice(item.product);
              
              return (
                <div key={item.id} className="flex gap-3">
                  <div className="flex-shrink-0">
                    <img
                      src={item.product.images[0] || "/placeholder.png"}
                      alt={item.product.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium truncate">
                      {item.product.name}
                    </h4>
                    <div className="flex items-center gap-1 mt-1">
                      {item.size && (
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {item.size}
                        </Badge>
                      )}
                      {item.color && (
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {item.color}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-sm text-gray-600">
                        Qty: {item.quantity}
                      </span>
                      <span className="text-sm font-medium">
                        {formatPrice(effectivePrice * item.quantity)}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Discount Code */}
      {currentStep <= 2 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Discount Code
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!checkoutData.discountCode ? (
              <div className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter discount code"
                    value={discountCode}
                    onChange={(e) => setDiscountCode(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && applyDiscountCode()}
                  />
                  <Button
                    onClick={applyDiscountCode}
                    disabled={!discountCode.trim() || isApplyingDiscount}
                    size="sm"
                  >
                    {isApplyingDiscount ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      "Apply"
                    )}
                  </Button>
                </div>
                {discountError && (
                  <p className="text-red-500 text-sm">{discountError}</p>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="text-green-800 font-medium">
                    {checkoutData.discountCode}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={removeDiscountCode}
                  className="text-green-600 hover:text-green-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Price Breakdown */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Subtotal ({cartState.totalItems} items)</span>
              <span>{formatPrice(subtotal)}</span>
            </div>
            
            {discountAmount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Discount {checkoutData.discountCode && `(${checkoutData.discountCode})`}</span>
                <span>-{formatPrice(discountAmount)}</span>
              </div>
            )}
            
            <div className="flex justify-between text-sm">
              <span>Delivery</span>
              <span className={deliveryInfo.isFree ? "text-green-600" : ""}>
                {deliveryInfo.isFree ? "Free" : formatPrice(deliveryCost)}
              </span>
            </div>
            {deliveryInfo.reason && (
              <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded mt-1">{deliveryInfo.reason}</div>
            )}
            
            <Separator />
            
            <div className="flex justify-between font-semibold text-lg">
              <span>Total</span>
              <span>{formatPrice(total)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Features */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-xs text-gray-500 space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Secure checkout process</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Email confirmation sent</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer Support */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <MessageCircle className="h-5 w-5 text-blue-600" />
            <div>
              <div className="font-medium text-blue-900">Need Help?</div>
              <div className="text-sm text-blue-700">
                Contact customer support: <strong>+266 6284 4473</strong>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
