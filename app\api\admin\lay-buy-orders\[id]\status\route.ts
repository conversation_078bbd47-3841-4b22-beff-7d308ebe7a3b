import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { updateLayBuyOrderStatus } from "@/actions/layBuyActions";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// PATCH /api/admin/lay-buy-orders/[id]/status - Update Lay-Buy order status
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { status, adminNotes } = body;

    if (!status) {
      return NextResponse.json(
        { success: false, error: "Status is required" },
        { status: 400 }
      );
    }

    const validStatuses = ["ACTIVE", "COMPLETED", "CANCELLED", "FORFEITED", "REFUNDED"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid status" },
        { status: 400 }
      );
    }

    const result = await updateLayBuyOrderStatus(paramsId, status, adminNotes);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    const response: ApiResponse<typeof result.data> = {
      success: true,
      data: result.data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating Lay-Buy order status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update order status" },
      { status: 500 }
    );
  }
}
