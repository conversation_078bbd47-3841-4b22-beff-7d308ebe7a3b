import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";


// GET /api/contact/[id] - Get single contact message (admin only)
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const {id: paramsId} = await context.params;

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const message = await prisma.contactMessage.findUnique({
      where: { id: paramsId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!message) {
      return NextResponse.json(
        { success: false, error: "Message not found" },
        { status: 404 }
      );
    }

    const response: ApiResponse<typeof message> = {
      success: true,
      data: message,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching contact message:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch message" },
      { status: 500 }
    );
  }
}

// PUT /api/contact/[id] - Update contact message status (admin only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const {id: paramsId} = await context.params;

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { status, adminNotes } = body;

    // Validate status
    const validStatuses = ["UNREAD", "READ", "REPLIED", "RESOLVED"];
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid status" },
        { status: 400 }
      );
    }

    const message = await prisma.contactMessage.findUnique({
      where: { id: paramsId },
    });

    if (!message) {
      return NextResponse.json(
        { success: false, error: "Message not found" },
        { status: 404 }
      );
    }

    const updatedMessage = await prisma.contactMessage.update({
      where: { id: paramsId },
      data: {
        ...(status && { status }),
        ...(adminNotes !== undefined && { adminNotes }),
        ...(status === "READ" && message.status === "UNREAD" && { readAt: new Date() }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const response: ApiResponse<typeof updatedMessage> = {
      success: true,
      data: updatedMessage,
      message: "Message updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating contact message:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update message" },
      { status: 500 }
    );
  }
}

// DELETE /api/contact/[id] - Delete contact message (admin only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const {id: paramsId} = await context.params;

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const message = await prisma.contactMessage.findUnique({
      where: { id: paramsId },
    });

    if (!message) {
      return NextResponse.json(
        { success: false, error: "Message not found" },
        { status: 404 }
      );
    }

    await prisma.contactMessage.delete({
      where: { id: paramsId },
    });

    const response: ApiResponse = {
      success: true,
      message: "Message deleted successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error deleting contact message:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete message" },
      { status: 500 }
    );
  }
}
