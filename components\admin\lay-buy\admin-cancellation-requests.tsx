"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Ban,
  Search,
  Filter,
  Calendar,
  User,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  RefreshCw,
  DollarSign,
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";
import { formatLayBuyPrice } from "@/lib/lay-buy-utils";

interface CancellationRequest {
  id: string;
  reason?: string;
  status: string;
  refundAmount?: number;
  adminNotes?: string;
  processedBy?: string;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
  layBuyOrder: {
    orderNumber: string;
    totalAmount: number;
    amountPaid: number;
    status: string;
    dueDate: string;
  };
  requestedBy: {
    name: string;
    email: string;
  };
}

export default function AdminCancellationRequests() {
  const [requests, setRequests] = useState<CancellationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedRequest, setSelectedRequest] = useState<CancellationRequest | null>(null);
  const [showProcessDialog, setShowProcessDialog] = useState(false);
  const [processingAction, setProcessingAction] = useState<"APPROVE" | "REJECT" | null>(null);
  const [adminNotes, setAdminNotes] = useState("");
  const [customRefundAmount, setCustomRefundAmount] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const fetchRequests = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(statusFilter !== "ALL" && { status: statusFilter }),
        ...(searchTerm && { search: searchTerm }),
      });

      const response = await fetch(`/api/admin/lay-buy-cancellation-requests?${params}`);
      const result = await response.json();

      if (result.success) {
        setRequests(result.data);
        setTotalPages(result.pagination?.totalPages || 1);
      }
    } catch (error) {
      console.error("Error fetching cancellation requests:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [currentPage, statusFilter, searchTerm]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="text-yellow-700 border-yellow-300">Pending</Badge>;
      case "APPROVED":
        return <Badge variant="outline" className="text-green-700 border-green-300">Approved</Badge>;
      case "REJECTED":
        return <Badge variant="outline" className="text-red-700 border-red-300">Rejected</Badge>;
      case "PROCESSED":
        return <Badge variant="outline" className="text-blue-700 border-blue-300">Processed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleProcessRequest = (request: CancellationRequest, action: "APPROVE" | "REJECT") => {
    setSelectedRequest(request);
    setProcessingAction(action);
    setAdminNotes("");
    setCustomRefundAmount(request.refundAmount?.toString() || "0");
    setShowProcessDialog(true);
  };

  const submitProcessRequest = async () => {
    if (!selectedRequest || !processingAction) return;

    setIsProcessing(true);
    try {
      const response = await fetch(`/api/admin/lay-buy-cancellation-requests/${selectedRequest.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: processingAction,
          adminNotes: adminNotes.trim(),
          ...(processingAction === "APPROVE" && customRefundAmount && {
            refundAmount: parseFloat(customRefundAmount),
          }),
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        setShowProcessDialog(false);
        setSelectedRequest(null);
        setProcessingAction(null);
        fetchRequests(); // Refresh the list
      } else {
        console.error("Failed to process request:", result.error);
      }
    } catch (error) {
      console.error("Error processing request:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Ban className="h-5 w-5" />
            Lay-Buy Cancellation Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by order number, customer name, or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
                <SelectItem value="PROCESSED">Processed</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={fetchRequests} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Requests List */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-500 mt-2">Loading cancellation requests...</p>
            </div>
          ) : requests.length === 0 ? (
            <div className="text-center py-8">
              <Ban className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No cancellation requests found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {requests.map((request) => (
                <Card key={request.id} className="border-l-4 border-l-orange-400">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">Order {request.layBuyOrder.orderNumber}</h3>
                          {getStatusBadge(request.status)}
                        </div>
                        <div className="text-sm text-gray-600 space-y-1">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>{request.requestedBy.name} ({request.requestedBy.email})</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>Requested: {new Date(request.createdAt).toLocaleDateString()}</span>
                          </div>
                          {request.refundAmount && (
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4" />
                              <span>Refund Amount: {formatLayBuyPrice(request.refundAmount)}</span>
                            </div>
                          )}
                          {request.reason && (
                            <div className="mt-2">
                              <span className="font-medium">Reason: </span>
                              <span className="text-gray-700">{request.reason}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {request.status === "PENDING" && (
                          <>
                            <Button
                              onClick={() => handleProcessRequest(request, "APPROVE")}
                              size="sm"
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button
                              onClick={() => handleProcessRequest(request, "REJECT")}
                              size="sm"
                              variant="destructive"
                            >
                              <XCircle className="h-4 w-4 mr-1" />
                              Reject
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-6">
              <Button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                variant="outline"
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                variant="outline"
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Process Request Dialog */}
      <Dialog open={showProcessDialog} onOpenChange={setShowProcessDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {processingAction === "APPROVE" ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              {processingAction === "APPROVE" ? "Approve" : "Reject"} Cancellation Request
            </DialogTitle>
            <DialogDescription>
              {selectedRequest && (
                <>
                  Order {selectedRequest.layBuyOrder.orderNumber} - {selectedRequest.requestedBy.name}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {processingAction === "APPROVE" && selectedRequest && (
              <div>
                <label className="text-sm font-medium">Refund Amount (M)</label>
                <Input
                  type="number"
                  step="0.01"
                  value={customRefundAmount}
                  onChange={(e) => setCustomRefundAmount(e.target.value)}
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Calculated: {formatLayBuyPrice(selectedRequest.refundAmount || 0)}
                </p>
              </div>
            )}

            <div>
              <label className="text-sm font-medium">Admin Notes</label>
              <Textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder="Add notes about this decision..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowProcessDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={submitProcessRequest}
              disabled={isProcessing}
              className={processingAction === "APPROVE" ? "bg-green-600 hover:bg-green-700" : ""}
              variant={processingAction === "REJECT" ? "destructive" : "default"}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  {processingAction === "APPROVE" ? (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  ) : (
                    <XCircle className="h-4 w-4 mr-2" />
                  )}
                  {processingAction === "APPROVE" ? "Approve" : "Reject"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
