"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Star,
  ShoppingBag,
  Heart,
  CheckCircle,
  Box,
  BoxIcon,
  BoxesIcon,
} from "lucide-react";
import NavBar from "@/components/navbar";
import Footer from "@/components/footer";
import { useSession } from "@/lib/auth-client";
import { User } from "@/utils/types";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import NavBarSkeleton from "@/components/navBarSkeleton";

// Featured products data with the provided images
const featuredProducts = [
  {
    id: 1,
    name: "Nike Air Max Plus",
    price: "M 1,320.00",
    originalPrice: "M 1,320.00",
    image: "https://utfs.io/f/9WixNlVtj4JyFteLp8cS2Fay4xCtT71WZglsqrhVPInO6UB9",
    badge: "Best Seller",
  },
  {
    id: 2,
    name: "Lacoste Legends",
    price: "M 1,200.00",
    originalPrice: "M 1,200.00",
    image: "https://utfs.io/f/9WixNlVtj4JyeT16EiuEBkJD2g5nOsQ9iuNZMSL4xRj0GIyA",
    badge: "New Arrival",
  },
  {
    id: 3,
    name: "Nike Air Force 1",
    price: "M 1,295.00",
    originalPrice: "M 1,295.00",
    image: "https://utfs.io/f/9WixNlVtj4Jy63U63mM7wnHZbhSTPO8qjNv0YBpceQuXyLKE",
    badge: "Limited Edition",
  },
  {
    id: 4,
    name: "New Balance 550 Model",
    price: "M 1,430.00",
    originalPrice: "M 1,430.00",
    image: "https://utfs.io/f/9WixNlVtj4JyVsObbllBOoK2RuYdTjBsJ0FcI64UVWlkLpD1",
    badge: "Premium",
  },
  {
    id: 5,
    name: "Nike Air Max 1",
    price: "M 1,310.00",
    originalPrice: "M 1,310.00",
    image: "https://utfs.io/f/9WixNlVtj4Jy5LsGp2aPNd75zEHMSJkm163pvcBtXUa0qRQC",
    badge: "Top Rated",
  },
  {
    id: 6,
    name: "Adidas EQT",
    price: "M 1,295.00",
    originalPrice: "M 1,295.00",
    image:
      "https://utfs.io/f/9WixNlVtj4JyZSx6vO8yBmMUpFoe6rqwluS1s3WOa29Cg5tf",
    badge: "Exclusive",
  },
];

const testimonials = [
  {
    name: "Thabo Mokoena",
    location: "Maseru",
    rating: 5,
    comment:
      "Exceptional quality and comfort. These sneakers exceeded my expectations!",
  },
  {
    name: "Nomsa Lebesa",
    location: "Mafeteng",
    rating: 5,
    comment:
      "Fast delivery and amazing customer service. Will definitely order again.",
  },
  {
    name: "Lerato Mthembu",
    location: "Leribe",
    rating: 5,
    comment: "Perfect fit and style. RIVV truly delivers premium quality.",
  },
];

export default function Home() {
  const { data: session, isPending } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      if (session?.user?.id) {
        try {
          const userResponse = await getUserById(session.user.id);
          if (userResponse.success && userResponse.data) {
            setUser(userResponse.data);
          }
        } catch (error) {
          console.error("Error fetching user:", error);
        }
      }
      setLoading(false);
    };

    if (!isPending) {
      fetchUser();
    }
  }, [session, isPending]);

  // if (isPending || loading) {
  //   return <SpinnerCircle4 />;
  // }

  return (
    <div className="min-h-screen bg-white">
      {user ? (
        <NavBar
          user={user || (session?.user as User)}
          loading={isPending || loading}
        />
      ) : (
        <NavBarSkeleton loading={isPending || loading} user={null} />
      )}

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 to-gray-100 py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                  Proudly Female-Founded
                </Badge>
                <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Purposefully Curated.{" "}
                  <span className="text-blue-600">
                    Unapologetically Premium.
                  </span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  RIVV Premium Sneakers is on a mission to deliver quality that
                  speaks for itself. Every pair is carefully selected for its
                  craftsmanship, comfort, and standout style.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  asChild
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Link href="/products">
                    Shop Collection <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[0].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[1].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>
                <div className="space-y-4 pt-8">
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[2].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[3].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              Featured Collection
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover our handpicked selection of premium sneakers, each chosen
              for exceptional quality and style.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <Card
                key={product.id}
                className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
              >
                <CardContent className="p-0">
                  <div className="relative aspect-square overflow-hidden rounded-t-lg">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-white/90 text-gray-900 hover:bg-white">
                        {product.badge}
                      </Badge>
                    </div>
                    <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="rounded-full w-10 h-10 p-0"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="p-6 space-y-4">
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                        {product.name}
                      </h3>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-2xl font-bold text-gray-900">
                          {product.originalPrice}
                        </span>
                        {/* <span className="text-lg text-gray-500 line-through">{product.originalPrice}</span> */}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      {/* <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                        <span className="text-sm text-gray-600 ml-1">(4.9)</span>
                      </div> */}
                      <Link href={"/products"}>
                        <Button
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <BoxesIcon className="h-4 w-4 mr-2" />
                          View Products
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button asChild size="lg" variant="outline">
              <Link href="/products">
                View All Products <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
                  We're Not Here to Meet Expectations—We're Here to Exceed Them
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  RIVV Premium Sneakers is a proudly female-founded brand on a
                  mission to deliver quality that speaks for itself. Every pair
                  is carefully selected for its craftsmanship, comfort, and
                  standout style.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Step in with confidence. You won't be disappointed.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-8">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Premium Quality
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Carefully curated for exceptional craftsmanship
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Comfort First
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Designed for all-day comfort and support
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Standout Style
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Unique designs that make a statement
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Female-Founded
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Proudly supporting women in business
                  </p>
                </div>
              </div>

              <Button asChild size="lg">
                <Link href="/contact">
                  Learn More About Us <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="relative aspect-[4/5] rounded-2xl overflow-hidden">
                  <Image
                    src={featuredProducts[4].image}
                    alt="RIVV Premium Sneakers"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-[4/5] rounded-2xl overflow-hidden mt-8">
                  <Image
                    src={featuredProducts[5].image}
                    alt="RIVV Premium Sneakers"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              What Our Customers Say
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our satisfied
              customers across Lesotho have to say.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className="p-6 hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0 space-y-4">
                  <div className="flex items-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  <p className="text-gray-700 italic">
                    "{testimonial.comment}"
                  </p>
                  <div className="border-t pt-4">
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.location}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
