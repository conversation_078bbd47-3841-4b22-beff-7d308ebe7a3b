"use client";

import { useState, useEffect } from "react";
import { Product } from "@/utils/types";
import ProductCard from "./product-card";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface RelatedProductsProps {
  currentProduct: Product;
  categoryId: string;
}

export default function RelatedProducts({ currentProduct, categoryId }: RelatedProductsProps) {
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);

  const itemsPerPage = 4;

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      try {
        setLoading(true);
        
        // Fetch products from the same category, excluding current product
        const response = await fetch(
          `/api/products?categoryId=${categoryId}&limit=12`
        );
        const result = await response.json();

        if (result.success) {
          // Filter out current product and limit to 8 items
          const filtered = result.data.data
            .filter((product: Product) => product.id !== currentProduct.id)
            .slice(0, 8);
          setRelatedProducts(filtered);
        }
      } catch (error) {
        console.error("Error fetching related products:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRelatedProducts();
  }, [categoryId, currentProduct.id]);

  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Related Products</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (relatedProducts.length === 0) {
    return null;
  }

  const maxIndex = Math.max(0, relatedProducts.length - itemsPerPage);
  const visibleProducts = relatedProducts.slice(currentIndex, currentIndex + itemsPerPage);

  const goToPrevious = () => {
    setCurrentIndex(Math.max(0, currentIndex - itemsPerPage));
  };

  const goToNext = () => {
    setCurrentIndex(Math.min(maxIndex, currentIndex + itemsPerPage));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Related Products</h2>
        
        {relatedProducts.length > itemsPerPage && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPrevious}
              disabled={currentIndex === 0}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNext}
              disabled={currentIndex >= maxIndex}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {visibleProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {relatedProducts.length > itemsPerPage && (
        <div className="flex justify-center">
          <div className="flex items-center gap-2">
            {Array.from({ length: Math.ceil(relatedProducts.length / itemsPerPage) }).map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  Math.floor(currentIndex / itemsPerPage) === index
                    ? "bg-blue-600"
                    : "bg-gray-300"
                }`}
                onClick={() => setCurrentIndex(index * itemsPerPage)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
