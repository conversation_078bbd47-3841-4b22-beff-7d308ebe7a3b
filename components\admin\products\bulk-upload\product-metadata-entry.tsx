"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Plus,
  Trash2,
  Copy,
  Save,
  ArrowLeft,
  ArrowRight,
  Package,
  AlertCircle,
  Wand2,
  Download,
  Upload,
} from "lucide-react";
import { UploadedImage, ProductData } from "./bulk-upload-content";
import ProductImageAssignment from "./product-image-assignment";

interface ProductMetadataEntryProps {
  uploadedImages: UploadedImage[];
  categories: Array<{ id: string; name: string }>;
  loadingCategories: boolean;
  onProductsCreated: (products: ProductData[]) => void;
  onBack: () => void;
}

interface ProductTemplate {
  name: string;
  brand: string;
  categoryId: string;
  sizes: string[];
  price?: number;
  discountedPrice?: number;
}

const defaultSizes = ["3", "4", "5", "6", "7", "8", "9", "10"];

export default function ProductMetadataEntry({
  uploadedImages,
  categories = [{ id: "456", name: "Shoes" }],
  loadingCategories,
  onProductsCreated,
  onBack,
}: ProductMetadataEntryProps) {
  const [products, setProducts] = useState<ProductData[]>([]);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [templates, setTemplates] = useState<ProductTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string[]>
  >({});
  const [viewMode, setViewMode] = useState<"form" | "table">("form");
  const [selectedProducts, setSelectedProducts] = useState<Set<number>>(
    new Set()
  );
  const [batchOperations, setBatchOperations] = useState({
    brand: "",
    categoryId: "456",
    sizes: [] as string[],
    price: "",
    discountedPrice: "",
    stock: "",
  });
  const [showBatchDialog, setShowBatchDialog] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Initialize with one empty product
  useEffect(() => {
    if (products.length === 0) {
      addNewProduct();
    }
  }, []);

  // Auto-save functionality
  const saveToLocalStorage = useCallback(() => {
    if (autoSaveEnabled && products.length > 0) {
      try {
        localStorage.setItem(
          "bulk-upload-products",
          JSON.stringify({
            products,
            templates,
            timestamp: new Date().toISOString(),
          })
        );
        setLastSaved(new Date());
      } catch (error) {
        console.error("Failed to auto-save:", error);
      }
    }
  }, [products, templates, autoSaveEnabled]);

  // Auto-save every 30 seconds
  useEffect(() => {
    if (autoSaveEnabled) {
      const interval = setInterval(saveToLocalStorage, 30000);
      return () => clearInterval(interval);
    }
  }, [saveToLocalStorage, autoSaveEnabled]);

  // Load from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem("bulk-upload-products");
      if (saved) {
        const data = JSON.parse(saved);
        if (data.products && data.products.length > 0) {
          setProducts(data.products);
          if (data.templates) {
            setTemplates(data.templates);
          }
          setLastSaved(new Date(data.timestamp));
        }
      }
    } catch (error) {
      console.error("Failed to load saved data:", error);
    }
  }, []);

  // Save when products change
  useEffect(() => {
    const timeoutId = setTimeout(saveToLocalStorage, 1000);
    return () => clearTimeout(timeoutId);
  }, [products, saveToLocalStorage]);

  const createEmptyProduct = (): ProductData => ({
    id: `product-${Date.now()}-${Math.random()}`,
    name: "",
    description: "",
    price: 0,
    discountedPrice: undefined,
    brand: "",
    categoryId: "456",
    images: [],
    sizes: [],
    stock: 0,
    isActive: true,
  });

  const addNewProduct = () => {
    const newProduct = createEmptyProduct();
    setProducts((prev) => [...prev, newProduct]);
    setCurrentProductIndex(products.length);
  };

  const removeProduct = (index: number) => {
    if (products.length > 1) {
      setProducts((prev) => prev.filter((_, i) => i !== index));
      if (currentProductIndex >= products.length - 1) {
        setCurrentProductIndex(Math.max(0, currentProductIndex - 1));
      }
    }
  };

  const duplicateProduct = (index: number) => {
    const productToDuplicate = products[index];
    const duplicatedProduct: ProductData = {
      ...productToDuplicate,
      id: `product-${Date.now()}-${Math.random()}`,
      name: `${productToDuplicate.name} (Copy)`,
      images: [], // Don't duplicate image assignments
    };
    setProducts((prev) => {
      const newProducts = [...prev];
      newProducts.splice(index + 1, 0, duplicatedProduct);
      return newProducts;
    });
    setCurrentProductIndex(index + 1);
  };

  const updateProduct = (index: number, updates: Partial<ProductData>) => {
    setProducts((prev) =>
      prev.map((product, i) =>
        i === index ? { ...product, ...updates } : product
      )
    );

    // Clear validation errors for updated fields
    if (validationErrors[index]) {
      const updatedErrors = { ...validationErrors };
      Object.keys(updates).forEach((key) => {
        if (updatedErrors[index]) {
          updatedErrors[index] = updatedErrors[index].filter(
            (error) => !error.includes(key)
          );
          if (updatedErrors[index].length === 0) {
            delete updatedErrors[index];
          }
        }
      });
      setValidationErrors(updatedErrors);
    }
  };

  const validateProducts = () => {
    const errors: Record<string, string[]> = {};

    products.forEach((product, index) => {
      const productErrors: string[] = [];

      if (!product.name.trim()) {
        productErrors.push("Product name is required");
      }
      if (!product.price || product.price <= 0) {
        productErrors.push("Valid price is required");
      }
      if (!product.brand.trim()) {
        productErrors.push("Brand is required");
      }
      if (!product.categoryId) {
        productErrors.push("Category is required");
      }
      if (product.discountedPrice && product.discountedPrice >= product.price) {
        productErrors.push("Discounted price must be less than regular price");
      }

      if (productErrors.length > 0) {
        errors[index] = productErrors;
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const saveAsTemplate = () => {
    const currentProduct = products[currentProductIndex];
    if (currentProduct && currentProduct.name && currentProduct.brand) {
      const template: ProductTemplate = {
        name: currentProduct.name,
        brand: currentProduct.brand,
        categoryId: currentProduct.categoryId,
        sizes: currentProduct.sizes,
        price: currentProduct.price,
        discountedPrice: currentProduct.discountedPrice,
      };
      setTemplates((prev) => [...prev, template]);
    }
  };

  const applyTemplate = (templateIndex: number) => {
    const template = templates[templateIndex];
    if (template) {
      updateProduct(currentProductIndex, {
        brand: template.brand,
        categoryId: template.categoryId,
        sizes: template.sizes,
        price: template.price,
        discountedPrice: template.discountedPrice,
      });
    }
  };

  const toggleProductSelection = (index: number) => {
    setSelectedProducts((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const selectAllProducts = () => {
    if (selectedProducts.size === products.length) {
      setSelectedProducts(new Set());
    } else {
      setSelectedProducts(new Set(products.map((_, index) => index)));
    }
  };

  const applyBatchOperations = () => {
    const updates: Partial<ProductData> = {};

    if (batchOperations.brand) updates.brand = batchOperations.brand;
    if (batchOperations.categoryId)
      updates.categoryId = batchOperations.categoryId;
    if (batchOperations.sizes.length > 0) updates.sizes = batchOperations.sizes;
    if (batchOperations.price)
      updates.price = parseFloat(batchOperations.price);
    if (batchOperations.discountedPrice)
      updates.discountedPrice = parseFloat(batchOperations.discountedPrice);
    if (batchOperations.stock) updates.stock = parseInt(batchOperations.stock);

    selectedProducts.forEach((index) => {
      updateProduct(index, updates);
    });

    setShowBatchDialog(false);
    setSelectedProducts(new Set());
    setBatchOperations({
      brand: "",
      categoryId: "456",
      sizes: [],
      price: "",
      discountedPrice: "",
      stock: "",
    });
  };

  const exportProducts = () => {
    const dataStr = JSON.stringify(products, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `bulk-products-${
      new Date().toISOString().split("T")[0]
    }.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const importProducts = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedProducts = JSON.parse(e.target?.result as string);
          if (Array.isArray(importedProducts)) {
            setProducts(
              importedProducts.map((p) => ({
                ...p,
                id: `product-${Date.now()}-${Math.random()}`,
              }))
            );
          }
        } catch (error) {
          console.error("Failed to import products:", error);
        }
      };
      reader.readAsText(file);
    }
  };

  const handleSubmit = () => {
    if (validateProducts()) {
      onProductsCreated(products);
    }
  };

  const currentProduct = products[currentProductIndex];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Package className="w-8 h-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Product Details
            </h2>
            <p className="text-gray-600">
              Enter product information for each item
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            {lastSaved && (
              <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
            )}
            <Badge variant={autoSaveEnabled ? "default" : "outline"}>
              Auto-save {autoSaveEnabled ? "ON" : "OFF"}
            </Badge>
          </div>
          <Button variant="outline" size="sm" onClick={exportProducts}>
            <Download className="w-4 h-4 mr-1" />
            Export
          </Button>
          <div>
            <input
              type="file"
              accept=".json"
              onChange={importProducts}
              className="hidden"
              id="import-products"
            />
            <Button variant="outline" size="sm" asChild>
              <label htmlFor="import-products" className="cursor-pointer">
                <Upload className="w-4 h-4 mr-1" />
                Import
              </label>
            </Button>
          </div>
          <Tabs
            value={viewMode}
            onValueChange={(value) => setViewMode(value as "form" | "table")}
          >
            <TabsList>
              <TabsTrigger value="form">Form View</TabsTrigger>
              <TabsTrigger value="table">Table View</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Product Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700">
                Product {currentProductIndex + 1} of {products.length}
              </span>
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentProductIndex(Math.max(0, currentProductIndex - 1))
                  }
                  disabled={currentProductIndex === 0}
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCurrentProductIndex(
                      Math.min(products.length - 1, currentProductIndex + 1)
                    )
                  }
                  disabled={currentProductIndex === products.length - 1}
                >
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span
                className="inline-flex items-center border rounded-md px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 mr-2"
                onClick={selectAllProducts}
                role="button"
                tabIndex={0}
                onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') selectAllProducts(); }}
              >
                <Checkbox
                  checked={
                    selectedProducts.size === products.length &&
                    products.length > 0
                  }
                  className="mr-1"
                />
                Select All
              </span>
              {selectedProducts.size > 0 && (
                <Dialog
                  open={showBatchDialog}
                  onOpenChange={setShowBatchDialog}
                >
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Wand2 className="w-4 h-4 mr-1" />
                      Batch Edit ({selectedProducts.size})
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Batch Operations</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Brand</Label>
                        <Input
                          value={batchOperations.brand}
                          onChange={(e) =>
                            setBatchOperations((prev) => ({
                              ...prev,
                              brand: e.target.value,
                            }))
                          }
                          placeholder="Apply to selected products"
                        />
                      </div>
                      <div>
                        <Label>Category</Label>
                        <Select
                          value={batchOperations.categoryId}
                          onValueChange={(value) =>
                            setBatchOperations((prev) => ({
                              ...prev,
                              categoryId: "456",
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Shoes">Shoes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Price (M)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={batchOperations.price}
                          onChange={(e) =>
                            setBatchOperations((prev) => ({
                              ...prev,
                              price: e.target.value,
                            }))
                          }
                          placeholder="Apply to selected products"
                        />
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => setShowBatchDialog(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={applyBatchOperations}>
                          Apply to {selectedProducts.size} Products
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
              <Button variant="outline" size="sm" onClick={addNewProduct}>
                <Plus className="w-4 h-4 mr-1" />
                Add Product
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => duplicateProduct(currentProductIndex)}
              >
                <Copy className="w-4 h-4 mr-1" />
                Duplicate
              </Button>
              {products.length > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeProduct(currentProductIndex)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Remove
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates */}
      {templates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Templates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 flex-wrap gap-2">
              {templates.map((template, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => applyTemplate(index)}
                >
                  {template.name} - {template.brand}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Validation Errors */}
      {validationErrors[currentProductIndex] && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {validationErrors[currentProductIndex].map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {viewMode === "form" && currentProduct && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Product Name *</Label>
                <Input
                  id="name"
                  value={currentProduct.name}
                  onChange={(e) =>
                    updateProduct(currentProductIndex, { name: e.target.value })
                  }
                  placeholder="Enter product name"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={currentProduct.description}
                  onChange={(e) =>
                    updateProduct(currentProductIndex, {
                      description: e.target.value,
                    })
                  }
                  placeholder="Enter product description"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="brand">Brand *</Label>
                <Input
                  id="brand"
                  value={currentProduct.brand}
                  onChange={(e) =>
                    updateProduct(currentProductIndex, {
                      brand: e.target.value,
                    })
                  }
                  placeholder="Enter brand name"
                />
              </div>

              <div>
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={currentProduct.categoryId}
                  onValueChange={(value) =>
                    updateProduct(currentProductIndex, { categoryId: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Shoes">Shoes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Pricing & Stock */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing & Stock</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="price">Price (M) *</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={currentProduct.price || ""}
                  onChange={(e) =>
                    updateProduct(currentProductIndex, {
                      price: parseFloat(e.target.value) || 0,
                    })
                  }
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="discountedPrice">Discounted Price (M)</Label>
                <Input
                  id="discountedPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  value={currentProduct.discountedPrice || ""}
                  onChange={(e) =>
                    updateProduct(currentProductIndex, {
                      discountedPrice: e.target.value
                        ? parseFloat(e.target.value)
                        : undefined,
                    })
                  }
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="stock">Stock Quantity</Label>
                <Input
                  id="stock"
                  type="number"
                  min="0"
                  value={currentProduct.stock || ""}
                  onChange={(e) =>
                    updateProduct(currentProductIndex, {
                      stock: parseInt(e.target.value) || 0,
                    })
                  }
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={saveAsTemplate}>
                  <Save className="w-4 h-4 mr-1" />
                  Save as Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Variants & Images */}
      {viewMode === "form" && currentProduct && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sizes */}
          <Card>
            <CardHeader>
              <CardTitle>Product Variants</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Available Sizes</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allSizesSelected = defaultSizes.every(size =>
                        currentProduct.sizes.includes(size)
                      );
                      const sizes = allSizesSelected
                        ? currentProduct.sizes.filter(size => !defaultSizes.includes(size))
                        : [...new Set([...currentProduct.sizes, ...defaultSizes])];
                      updateProduct(currentProductIndex, { sizes });
                    }}
                  >
                    {defaultSizes.every(size => currentProduct.sizes.includes(size))
                      ? "Deselect All"
                      : "Select All"}
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {defaultSizes.map((size) => (
                    <Badge
                      key={size}
                      variant={
                        currentProduct.sizes.includes(size)
                          ? "default"
                          : "outline"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const sizes = currentProduct.sizes.includes(size)
                          ? currentProduct.sizes.filter((s) => s !== size)
                          : [...currentProduct.sizes, size];
                        updateProduct(currentProductIndex, { sizes });
                      }}
                    >
                      {size}
                    </Badge>
                  ))}
                </div>
                <Input
                  className="mt-2"
                  placeholder="Add custom size"
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      const value = (e.target as HTMLInputElement).value.trim();
                      if (value && !currentProduct.sizes.includes(value)) {
                        updateProduct(currentProductIndex, {
                          sizes: [...currentProduct.sizes, value],
                        });
                        (e.target as HTMLInputElement).value = "";
                      }
                    }
                  }}
                />
              </div>
            </CardContent>
          </Card>

          {/* Image Assignment */}
          <Card>
            <CardHeader>
              <CardTitle>Product Images</CardTitle>
            </CardHeader>
            <CardContent>
              <ProductImageAssignment
                uploadedImages={uploadedImages}
                assignedImages={currentProduct.images}
                onImagesAssigned={(images) =>
                  updateProduct(currentProductIndex, { images })
                }
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Table View */}
      {viewMode === "table" && (
        <Card>
          <CardHeader>
            <CardTitle>All Products - Table View</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-2 text-left">
                      <Checkbox
                        checked={
                          selectedProducts.size === products.length &&
                          products.length > 0
                        }
                        onCheckedChange={selectAllProducts}
                      />
                    </th>
                    <th className="border border-gray-300 p-2 text-left">#</th>
                    <th className="border border-gray-300 p-2 text-left">
                      Name
                    </th>
                    <th className="border border-gray-300 p-2 text-left">
                      Brand
                    </th>
                    <th className="border border-gray-300 p-2 text-left">
                      Category
                    </th>
                    <th className="border border-gray-300 p-2 text-left">
                      Price (M)
                    </th>
                    <th className="border border-gray-300 p-2 text-left">
                      Stock
                    </th>
                    <th className="border border-gray-300 p-2 text-left">
                      Images
                    </th>
                    <th className="border border-gray-300 p-2 text-left">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {products.map((product, index) => (
                    <tr
                      key={product.id}
                      className={`${
                        index === currentProductIndex ? "bg-blue-50" : ""
                      } ${selectedProducts.has(index) ? "bg-yellow-50" : ""}`}
                    >
                      <td className="border border-gray-300 p-2">
                        <Checkbox
                          checked={selectedProducts.has(index)}
                          onCheckedChange={() => toggleProductSelection(index)}
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        {index + 1}
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          value={product.name}
                          onChange={(e) =>
                            updateProduct(index, { name: e.target.value })
                          }
                          className="w-full"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          value={product.brand}
                          onChange={(e) =>
                            updateProduct(index, { brand: e.target.value })
                          }
                          className="w-full"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Select
                          value={product.categoryId}
                          onValueChange={(value) =>
                            updateProduct(index, { categoryId: value })
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Shoes">Shoes</SelectItem>
                          </SelectContent>
                        </Select>
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          type="number"
                          step="0.01"
                          value={product.price || ""}
                          onChange={(e) =>
                            updateProduct(index, {
                              price: parseFloat(e.target.value) || 0,
                            })
                          }
                          className="w-full"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Input
                          type="number"
                          value={product.stock || ""}
                          onChange={(e) =>
                            updateProduct(index, {
                              stock: parseInt(e.target.value) || 0,
                            })
                          }
                          className="w-full"
                        />
                      </td>
                      <td className="border border-gray-300 p-2">
                        <Badge variant="outline">
                          {product.images.length} assigned
                        </Badge>
                      </td>
                      <td className="border border-gray-300 p-2">
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentProductIndex(index)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => duplicateProduct(index)}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          {products.length > 1 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeProduct(index)}
                              className="text-red-600"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Images
        </Button>
        <div className="flex space-x-4">
          <Badge variant="outline" className="px-3 py-1">
            {products.length} products • {Object.keys(validationErrors).length}{" "}
            errors
          </Badge>
          <Button
            onClick={handleSubmit}
            // disabled={Object.keys(validationErrors).length > 0}
            className="px-8"
          >
            Continue to Preview
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
