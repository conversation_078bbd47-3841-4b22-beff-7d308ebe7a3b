-- CreateEnum for Lay-Buy System
CREATE TYPE "LayBuyStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'CANCELLED', 'FORFEITED', 'REFUNDED');
CREATE TYPE "LayBuyPaymentType" AS ENUM ('UPFRONT', 'INSTALLMENT', 'COMPLETION');
CREATE TYPE "ReminderType" AS ENUM ('WEEKLY', 'URGENT', 'GRACE_PERIOD', 'FINAL_NOTICE');

-- CreateTable
CREATE TABLE "lay_buy_order" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "status" "LayBuyStatus" NOT NULL DEFAULT 'ACTIVE',
    "totalAmount" DOUBLE PRECISION NOT NULL,
    "upfrontAmount" DOUBLE PRECISION NOT NULL,
    "remainingAmount" DOUBLE PRECISION NOT NULL,
    "amountPaid" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "dueDate" TIMESTAMP(3) NOT NULL,
    "gracePeriodEnd" TIMESTAMP(3) NOT NULL,
    "shippingAddress" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "notes" TEXT,
    "adminNotes" TEXT,
    "cancelledAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "forfeitedAt" TIMESTAMP(3),
    "refundAmount" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lay_buy_order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lay_buy_order_item" (
    "id" TEXT NOT NULL,
    "layBuyOrderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "size" TEXT,
    "color" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lay_buy_order_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lay_buy_payment" (
    "id" TEXT NOT NULL,
    "layBuyOrderId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "paymentType" "LayBuyPaymentType" NOT NULL,
    "paymentMethod" TEXT,
    "paymentProof" TEXT,
    "status" "PaymentStatus" NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lay_buy_payment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lay_buy_reminder" (
    "id" TEXT NOT NULL,
    "layBuyOrderId" TEXT NOT NULL,
    "weekNumber" INTEGER NOT NULL,
    "sentAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "emailSent" BOOLEAN NOT NULL DEFAULT false,
    "smsSent" BOOLEAN NOT NULL DEFAULT false,
    "reminderType" "ReminderType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lay_buy_reminder_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "lay_buy_order_orderNumber_key" ON "lay_buy_order"("orderNumber");

-- AddForeignKey
ALTER TABLE "lay_buy_order" ADD CONSTRAINT "lay_buy_order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_order_item" ADD CONSTRAINT "lay_buy_order_item_layBuyOrderId_fkey" FOREIGN KEY ("layBuyOrderId") REFERENCES "lay_buy_order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_order_item" ADD CONSTRAINT "lay_buy_order_item_productId_fkey" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_payment" ADD CONSTRAINT "lay_buy_payment_layBuyOrderId_fkey" FOREIGN KEY ("layBuyOrderId") REFERENCES "lay_buy_order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_reminder" ADD CONSTRAINT "lay_buy_reminder_layBuyOrderId_fkey" FOREIGN KEY ("layBuyOrderId") REFERENCES "lay_buy_order"("id") ON DELETE CASCADE ON UPDATE CASCADE;
