"use client";

import { useState } from "react";
import { CheckoutData } from "./checkout-content";
import { useCart } from "@/contexts/cart-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Upload, CreditCard, Building, AlertCircle, CheckCircle, Smartphone, MessageCircle } from "lucide-react";
import { formatPrice, calculateDeliveryFee } from "@/lib/product-utils";
import { UploadDropzone } from "@/lib/uploadthing";
import { useRouter } from "next/navigation";

interface CheckoutStepPaymentProps {
  data: CheckoutData;
  onPrev: () => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

export default function CheckoutStepPayment({ 
  data, 
  onPrev, 
  isProcessing, 
  setIsProcessing 
}: CheckoutStepPaymentProps) {
  const [paymentMethod, setPaymentMethod] = useState("mpesa");
  const [paymentProofUrl, setPaymentProofUrl] = useState<string>("");
  const [uploadError, setUploadError] = useState<string>("");
  const [orderPlaced, setOrderPlaced] = useState(false);
  const [orderNumber, setOrderNumber] = useState<string>("");

  const { state: cartState, clearCart } = useCart();
  const router = useRouter();

  const subtotal = cartState.totalPrice;
  const discountAmount = data.discountAmount || 0;

  // Calculate delivery fee based on district and order amount
  const deliveryInfo = calculateDeliveryFee(data.shipping.district, subtotal - discountAmount);
  const deliveryCost = deliveryInfo.fee;
  const baseTotal = subtotal - discountAmount + deliveryCost;

  // For Lay-Buy orders, customer only pays the upfront amount
  const isLayBuyOrder = data.layBuy?.enabled;
  const paymentAmount = isLayBuyOrder ? data.layBuy!.upfrontAmount : baseTotal;
  const total = baseTotal; // Keep total for display purposes

  const handlePlaceOrder = async () => {
    if (!paymentProofUrl) {
      setUploadError("Please upload payment proof before placing the order");
      return;
    }

    setIsProcessing(true);
    setUploadError("");

    try {
      // Determine which API endpoint to use based on order type
      const apiEndpoint = isLayBuyOrder ? "/api/lay-buy-orders" : "/api/orders";

      const orderData = {
        shippingAddress: `${data.shipping.address}, ${data.shipping.city}, ${data.shipping.district}, ${data.shipping.postalCode}, ${data.shipping.country}`,
        phoneNumber: data.shipping.phone,
        notes: data.shipping.notes,
        discountAmount: discountAmount,
        discountCode: data.discountCode,
        paymentMethod: paymentMethod,
        paymentProofUrl: paymentProofUrl,
        deliveryFee: deliveryCost,
        items: cartState.items.map(item => ({
          productId: item.product.id,
          quantity: item.quantity,
          price: item.product.discountedPrice || item.product.price,
          size: item.size,
          color: item.color,
        })),
        // Add Lay-Buy specific data if applicable
        ...(isLayBuyOrder && {
          layBuy: {
            totalAmount: baseTotal,
            upfrontAmount: data.layBuy!.upfrontAmount,
            remainingAmount: data.layBuy!.remainingAmount,
          }
        })
      };

      const orderResponse = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(orderData),
      });

      const orderResult = await orderResponse.json();

      if (orderResult.success) {
        setOrderNumber(orderResult.data.orderNumber);
        setOrderPlaced(true);
        clearCart();

        // Redirect to appropriate confirmation page after a delay
        setTimeout(() => {
          if (isLayBuyOrder) {
            router.push(`/lay-buy-orders/${orderResult.data.id}`);
          } else {
            router.push(`/orders/${orderResult.data.id}`);
          }
        }, 3000);
      } else {
        throw new Error(orderResult.error || "Failed to place order");
      }
    } catch (error) {
      console.error("Error placing order:", error);
      setUploadError(error instanceof Error ? error.message : "Failed to place order");
    } finally {
      setIsProcessing(false);
    }
  };

  if (orderPlaced) {
    return (
      <div className="text-center py-8">
        <div className="mb-6">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {isLayBuyOrder ? "Lay-Buy Order Created Successfully!" : "Order Placed Successfully!"}
        </h2>
        <p className="text-gray-600 mb-2">
          Your order number is: <strong>{orderNumber}</strong>
        </p>
        {isLayBuyOrder ? (
          <div className="text-gray-600 mb-6 space-y-2">
            <p>Your upfront payment of <strong>{formatPrice(paymentAmount)}</strong> has been received.</p>
            <p>You have <strong>6 weeks</strong> to complete the remaining payment of <strong>{formatPrice(data.layBuy!.remainingAmount)}</strong>.</p>
            <p>You will receive weekly email reminders with payment instructions.</p>
          </div>
        ) : (
          <p className="text-gray-600 mb-6">
            You will receive an email confirmation shortly. We'll notify you when your payment is verified.
          </p>
        )}
        <div className="animate-pulse text-sm text-gray-500">
          Redirecting to order details...
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Customer Support Notice */}
      <Alert>
        <MessageCircle className="h-4 w-4" />
        <AlertDescription>
          Need help with payment? Contact customer support on WhatsApp: <strong>+266 6284 4473</strong>
        </AlertDescription>
      </Alert>

      {/* Payment Method Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Payment Method</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
            {/* M-Pesa */}
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value="mpesa" id="mpesa" />
              <Label htmlFor="mpesa" className="flex items-center gap-3 cursor-pointer flex-1">
                <Smartphone className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">M-Pesa</div>
                  <div className="text-sm text-gray-500">
                    Send money via M-Pesa and upload payment proof
                  </div>
                </div>
              </Label>
            </div>

            {/* EcoCash */}
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value="ecocash" id="ecocash" />
              <Label htmlFor="ecocash" className="flex items-center gap-3 cursor-pointer flex-1">
                <Smartphone className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="font-medium">EcoCash</div>
                  <div className="text-sm text-gray-500">
                    Send money via EcoCash and upload payment proof
                  </div>
                </div>
              </Label>
            </div>

            {/* Bank Transfer */}
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value="bank_transfer" id="bank_transfer" />
              <Label htmlFor="bank_transfer" className="flex items-center gap-3 cursor-pointer flex-1">
                <Building className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="font-medium">Bank Transfer</div>
                  <div className="text-sm text-gray-500">
                    Transfer to our bank account and upload payment proof
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* M-Pesa Details */}
      {paymentMethod === "mpesa" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">M-Pesa Payment Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-green-900 mb-3">Send money to:</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-700">Phone Number:</span>
                  <span className="font-medium">+266 5316 3354</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700">Account Holder:</span>
                  <span className="font-medium">Katleho Namane</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span className="text-green-700">Amount to Send:</span>
                  <span className="text-lg">{formatPrice(isLayBuyOrder ? paymentAmount : total)}</span>
                </div>
                {isLayBuyOrder && (
                  <div className="mt-2 text-sm text-green-600 font-medium">
                    Amount to send today: {formatPrice(paymentAmount)}
                  </div>
                )}
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Send the exact amount via M-Pesa to +266 5316 3354 (Katleho Namane).
                After sending, upload a screenshot of the payment confirmation below.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* EcoCash Details */}
      {paymentMethod === "ecocash" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">EcoCash Payment Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-orange-900 mb-3">Send money to:</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-orange-700">Phone Number:</span>
                  <span className="font-medium">+266 6284 4473</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-orange-700">Account Holder:</span>
                  <span className="font-medium">Katleho Namane</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span className="text-orange-700">Amount to Send:</span>
                  <span className="text-lg">{formatPrice(isLayBuyOrder ? paymentAmount : total)}</span>
                </div>
                {isLayBuyOrder && (
                  <div className="mt-2 text-sm text-orange-600 font-medium">
                    Amount to send today: {formatPrice(paymentAmount)}
                  </div>
                )}
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Send the exact amount via EcoCash to +266 6284 4473 (Katleho Namane).
                After sending, upload a screenshot of the payment confirmation below.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Bank Transfer Details */}
      {paymentMethod === "bank_transfer" && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Bank Transfer Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-900 mb-3">Transfer to:</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-700">Bank Name:</span>
                  <span className="font-medium">Standard Lesotho Bank</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Account Name:</span>
                  <span className="font-medium">Katleho Isabella Namane</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Account Number:</span>
                  <span className="font-medium">*************</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Branch:</span>
                  <span className="font-medium">City Branch</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Branch Code:</span>
                  <span className="font-medium">060667</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Reference:</span>
                  <span className="font-medium">KATLEHO</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-semibold">
                  <span className="text-blue-700">Amount to Transfer:</span>
                  <span className="text-lg">{formatPrice(total)}</span>
                </div>
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please use "KATLEHO" as the reference when making the transfer.
                After making the transfer, upload a screenshot or photo of the payment confirmation below.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Payment Proof Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Upload Payment Proof</CardTitle>
        </CardHeader>
        <CardContent>
          {!paymentProofUrl ? (
            <div className="space-y-4">
              <UploadDropzone
                endpoint="paymentProofUploader"
                onClientUploadComplete={(res) => {
                  console.log("Upload complete:", res);
                  if (res && res[0]) {
                    // Use the correct URL property from uploadthing response
                    const uploadedUrl = res[0].ufsUrl;
                    setPaymentProofUrl(uploadedUrl);
                    setUploadError("");
                  }
                }}
                onUploadError={(error: Error) => {
                  console.error("Upload error:", error);
                  setUploadError(error.message);
                }}
                config={{
                  mode: "auto",
                }}
              />
              {uploadError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{uploadError}</AlertDescription>
                </Alert>
              )}
              <p className="text-sm text-gray-500">
                Upload a clear screenshot or photo of your payment confirmation.
                Supported formats: JPG, PNG (max 8MB)
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-green-800">Payment proof uploaded successfully</span>
              </div>

              <div className="border rounded-lg p-4">
                <img
                  src={paymentProofUrl}
                  alt="Payment proof"
                  className="max-w-full h-auto max-h-64 mx-auto rounded"
                />
              </div>

              <Button
                variant="outline"
                onClick={() => setPaymentProofUrl("")}
                className="w-full"
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload Different Image
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Final Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Subtotal ({cartState.totalItems} items)</span>
              <span>{formatPrice(subtotal)}</span>
            </div>
            
            {discountAmount > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Discount</span>
                <span>-{formatPrice(discountAmount)}</span>
              </div>
            )}
            
            <div className="flex justify-between">
              <span>Delivery</span>
              <span className={deliveryInfo.isFree ? "text-green-600" : ""}>
                {deliveryInfo.isFree ? "Free" : formatPrice(deliveryCost)}
              </span>
            </div>
            {deliveryInfo.reason && (
              <div className="text-xs text-gray-500 -mt-2">
                {deliveryInfo.reason}
              </div>
            )}
            
            <Separator />

            {isLayBuyOrder ? (
              <>
                <div className="flex justify-between">
                  <span>Order Total</span>
                  <span>{formatPrice(total)}</span>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-800 mb-2">Lay-Buy Payment</div>
                  <div className="flex justify-between text-sm">
                    <span>Pay Today (60%)</span>
                    <span className="font-semibold text-green-600">{formatPrice(paymentAmount)}</span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Remaining (40%)</span>
                    <span>{formatPrice(data.layBuy!.remainingAmount)}</span>
                  </div>
                  <div className="text-xs text-blue-700 mt-2">
                    Complete remaining payment within 6 weeks
                  </div>
                </div>
              </>
            ) : (
              <div className="flex justify-between font-semibold text-lg">
                <span>Total</span>
                <span>{formatPrice(total)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-4">
        <Button variant="outline" onClick={onPrev} disabled={isProcessing}>
          <ArrowLeft className="mr-2 h-5 w-5" />
          Back to Review
        </Button>
        
        <Button
          onClick={handlePlaceOrder}
          size="lg"
          disabled={isProcessing || !paymentProofUrl}
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Placing Order...
            </>
          ) : (
            <>
              <CreditCard className="mr-2 h-5 w-5" />
              Place Order
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
