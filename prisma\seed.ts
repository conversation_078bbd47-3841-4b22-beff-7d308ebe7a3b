import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name: 'Sneakers' },
      update: {},
      create: {
        name: 'Sneakers',
        description: 'Premium sneakers and athletic shoes',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Running Shoes' },
      update: {},
      create: {
        name: 'Running Shoes',
        description: 'High-performance running shoes',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Basketball Shoes' },
      update: {},
      create: {
        name: 'Basketball Shoes',
        description: 'Professional basketball footwear',
        isActive: true,
      },
    }),
    prisma.category.upsert({
      where: { name: 'Casual Shoes' },
      update: {},
      create: {
        name: 'Casual Shoes',
        description: 'Everyday casual footwear',
        isActive: true,
      },
    }),
  ]);

  console.log('✅ Categories created');

  // Create sample products
  const products = [
    {
      name: 'Air Jordan 1 Retro High',
      description: 'Classic basketball shoe with premium leather construction',
      price: 170.00,
      discountedPrice: 150.00,
      brand: 'Nike',
      categoryId: categories[2].id, // Basketball Shoes
      images: ['/placeholder-shoe.jpg'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black/Red', 'White/Black'],
      stock: 50,
      isActive: true,
      rating: 4.8,
      reviewCount: 124,
    },
    {
      name: 'Adidas Ultraboost 22',
      description: 'Premium running shoe with responsive Boost midsole',
      price: 190.00,
      brand: 'Adidas',
      categoryId: categories[1].id, // Running Shoes
      images: ['/placeholder-shoe.jpg'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Core Black', 'Cloud White', 'Solar Red'],
      stock: 75,
      isActive: true,
      rating: 4.6,
      reviewCount: 89,
    },
    {
      name: 'Nike Air Force 1',
      description: 'Iconic basketball shoe, perfect for everyday wear',
      price: 110.00,
      brand: 'Nike',
      categoryId: categories[3].id, // Casual Shoes
      images: ['/placeholder-shoe.jpg'],
      sizes: ['6', '7', '8', '9', '10', '11', '12'],
      colors: ['White', 'Black', 'Triple White'],
      stock: 100,
      isActive: true,
      rating: 4.7,
      reviewCount: 256,
    },
    {
      name: 'Converse Chuck Taylor All Star',
      description: 'Classic canvas sneaker with timeless design',
      price: 65.00,
      discountedPrice: 55.00,
      brand: 'Converse',
      categoryId: categories[0].id, // Sneakers
      images: ['/placeholder-shoe.jpg'],
      sizes: ['6', '7', '8', '9', '10', '11'],
      colors: ['Black', 'White', 'Red', 'Navy'],
      stock: 80,
      isActive: true,
      rating: 4.4,
      reviewCount: 178,
    },
    {
      name: 'Vans Old Skool',
      description: 'Classic skate shoe with signature side stripe',
      price: 65.00,
      brand: 'Vans',
      categoryId: categories[0].id, // Sneakers
      images: ['/placeholder-shoe.jpg'],
      sizes: ['6', '7', '8', '9', '10', '11', '12'],
      colors: ['Black/White', 'Navy/White', 'Checkerboard'],
      stock: 60,
      isActive: true,
      rating: 4.5,
      reviewCount: 142,
    },
  ];

  for (const productData of products) {
    await prisma.product.create({
      data: productData,
    });
  }

  console.log('✅ Products created');

  // Create discount codes
  await prisma.discountCode.createMany({
    data: [
      {
        code: 'WELCOME10',
        description: 'Welcome discount for new customers',
        type: 'PERCENTAGE',
        value: 10,
        minAmount: 50,
        maxUses: 100,
        isActive: true,
        validUntil: new Date('2025-12-31'),
      },
      {
        code: 'SUMMER25',
        description: 'Summer sale discount',
        type: 'FIXED_AMOUNT',
        value: 25,
        minAmount: 100,
        maxUses: 50,
        isActive: true,
        validUntil: new Date('2025-08-31'),
      },
    ],
  });

  console.log('✅ Discount codes created');

  // Create default settings
  await prisma.settings.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      primaryColor: '#3b82f6',
    },
  });

  console.log('✅ Settings created');

  // Create sample notices
  await prisma.notice.createMany({
    data: [
      {
        title: 'Welcome to RIVV Premium Sneakers!',
        content: 'Thank you for visiting our store. Check out our latest collection of premium sneakers.',
        type: 'INFO',
        isActive: true,
        priority: 1,
      },
      {
        title: 'Free Shipping',
        content: 'Free shipping on orders over $100. Limited time offer!',
        type: 'SUCCESS',
        isActive: true,
        priority: 2,
      },
    ],
  });

  console.log('✅ Notices created');

  console.log('🎉 Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
