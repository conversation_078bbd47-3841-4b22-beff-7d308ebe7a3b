"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import NavBar from "@/components/navbar";
import ProductFilters from "@/components/products/product-filters";
import ProductGrid from "@/components/products/product-grid";
import ProductSort from "@/components/products/product-sort";
import { useSession } from "@/lib/auth-client";
import {
  Product,
  ProductFilters as ProductFiltersType,
  PaginatedResponse,
  User,
} from "@/utils/types";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Filter, X } from "lucide-react";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import NavBarSkeleton from "@/components/navBarSkeleton";

function ProductsPageContent() {
  const { data: session, isPending } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Parse filters from URL
  const getFiltersFromUrl = (): ProductFiltersType => {
    return {
      search: searchParams.get("search") || undefined,
      categoryId: searchParams.get("categoryId") || undefined,
      brand: searchParams.get("brand") || undefined,
      minPrice: searchParams.get("minPrice")
        ? parseFloat(searchParams.get("minPrice")!)
        : undefined,
      maxPrice: searchParams.get("maxPrice")
        ? parseFloat(searchParams.get("maxPrice")!)
        : undefined,
      sizes: searchParams.get("sizes")
        ? searchParams.get("sizes")!.split(",")
        : undefined,
      colors: searchParams.get("colors")
        ? searchParams.get("colors")!.split(",")
        : undefined,
      sortBy: (searchParams.get("sortBy") as any) || "name",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "asc",
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "12"),
    };
  };

  const [filters, setFilters] = useState<ProductFiltersType>(
    getFiltersFromUrl()
  );

  // Update URL when filters change
  const updateUrl = (newFilters: ProductFiltersType) => {
    const params = new URLSearchParams();

    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            params.set(key, value.join(","));
          }
        } else {
          params.set(key, value.toString());
        }
      }
    });

    router.push(`/products?${params.toString()}`);
  };

  // Fetch products
  const fetchProducts = async (currentFilters: ProductFiltersType) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (Array.isArray(value)) {
            if (value.length > 0) {
              params.set(key, value.join(","));
            }
          } else {
            params.set(key, value.toString());
          }
        }
      });

      const response = await fetch(`/api/products?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setProducts(result.data.data);
        setPagination(result.data.pagination);
      } else {
        setError(result.error || "Failed to fetch products");
      }
    } catch (err) {
      setError("Failed to fetch products");
      console.error("Error fetching products:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: ProductFiltersType) => {
    const updatedFilters = { ...newFilters, page: 1 }; // Reset to first page
    setFilters(updatedFilters);
    updateUrl(updatedFilters);
  };

  // Handle sort changes
  const handleSortChange = (
    sortBy: "name" | "price" | "rating" | "newest",
    sortOrder: "asc" | "desc"
  ) => {
    const updatedFilters = { ...filters, sortBy, sortOrder, page: 1 };
    setFilters(updatedFilters);
    updateUrl(updatedFilters);
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    const updatedFilters = { ...filters, page };
    setFilters(updatedFilters);
    updateUrl(updatedFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedFilters: ProductFiltersType = {
      sortBy: "name",
      sortOrder: "asc",
      page: 1,
      limit: 12,
    };
    setFilters(clearedFilters);
    updateUrl(clearedFilters);
  };

  // Fetch products when filters change
  useEffect(() => {
    fetchProducts(filters);
  }, [filters]);

  // Update filters when URL changes
  useEffect(() => {
    setFilters(getFiltersFromUrl());
  }, [searchParams]);

  // if (!session) {
  //   return (
  //     <div className="w-full h-screen flex items-center justify-center">
  //       <SpinnerCircle4 />
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen bg-gray-50">
      {isPending || !session ? (
        <NavBarSkeleton loading={isPending} user={null} />
      ) : (
        <NavBar user={session.user as User} />
      )}

      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Products</h1>
            <p className="text-gray-600">
              {pagination.total}{" "}
              {pagination.total === 1 ? "product" : "products"} found
            </p>
          </div>

          <div className="flex items-center gap-4 mt-4 md:mt-0">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="md:hidden"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>

            <ProductSort
              sortBy={filters.sortBy || "name"}
              sortOrder={filters.sortOrder || "asc"}
              onSortChange={handleSortChange}
            />

            {(filters.search ||
              filters.categoryId ||
              filters.brand ||
              filters.minPrice ||
              filters.maxPrice ||
              (filters.sizes && filters.sizes.length > 0) ||
              (filters.colors && filters.colors.length > 0)) && (
              <Button variant="ghost" onClick={clearFilters}>
                <X className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div
            className={`lg:w-64 ${showFilters ? "block" : "hidden lg:block"}`}
          >
            <ProductFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              products={products}
            />
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <SpinnerCircle4 />
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={() => fetchProducts(filters)}>
                  Try Again
                </Button>
              </div>
            ) : (
              <ProductGrid
                products={products}
                pagination={pagination}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProductsPage() {
  return (
    <Suspense
      fallback={
        <div className="w-full h-screen flex justify-center items-center">
          <SpinnerCircle4 />
        </div>
      }
    >
      <ProductsPageContent />
    </Suspense>
  );
}
