"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ProductSortProps {
  sortBy: 'name' | 'price' | 'rating' | 'newest';
  sortOrder: "asc" | "desc";
  onSortChange: (sortBy: 'name' | 'price' | 'rating' | 'newest', sortOrder: "asc" | "desc") => void;
}

export default function ProductSort({ sortBy, sortOrder, onSortChange }: ProductSortProps) {
  const sortOptions: {
    value: string;
    label: string;
    sortBy: 'name' | 'price' | 'rating' | 'newest';
    sortOrder: "asc" | "desc";
  }[] = [
    { value: "name-asc", label: "Name (A-Z)", sortBy: "name", sortOrder: "asc" },
    { value: "name-desc", label: "Name (Z-A)", sortBy: "name", sortOrder: "desc" },
    { value: "price-asc", label: "Price (Low to High)", sortBy: "price", sortOrder: "asc" },
    { value: "price-desc", label: "Price (High to Low)", sortBy: "price", sortOrder: "desc" },
    { value: "rating-desc", label: "Highest Rated", sortBy: "rating", sortOrder: "desc" },
    { value: "newest", label: "Newest First", sortBy: "newest", sortOrder: "desc" },
  ];

  const currentValue = `${sortBy}-${sortOrder}`;

  const handleSortChange = (value: string) => {
    const option = sortOptions.find(opt => opt.value === value);
    if (option) {
      onSortChange(option.sortBy, option.sortOrder);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600 whitespace-nowrap">Sort by:</span>
      <Select value={currentValue} onValueChange={handleSortChange}>
        <SelectTrigger className="w-48">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
