"use client";

import Spinner<PERSON>ircle4 from "@/components/customized/spinner/spinner-10";
import Content from "@/components/dashboard/content";
import NavBar from "@/components/navbar";
import Footer from "@/components/footer";
import { UserRoute } from "@/components/auth/protected-route";
import { useSession } from "@/lib/auth-client";
import { User, UserRole } from "@/utils/types";
import React, { Suspense, useEffect } from "react";
import { getUserById } from "@/actions/userActions";
import NavBarSkeleton from "@/components/navBarSkeleton";

const DashboardPage = () => {
  return (
    <UserRoute>
      <DashboardContent />
    </UserRoute>
  );
};

const DashboardContent = () => {
  const { data, isPending } = useSession();
  const [role, setRole] = React.useState<UserRole | null>(null);

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = data!.user as User & { role?: UserRole };

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        const userFull = await getUserById(user.id);
        if (userFull.success && userFull.data) {
          setRole(userFull.data.role);
          user.role = userFull.data.role;
        }
      }
    };
    getUserDetails();
  }, [user]);

  return (
    <div className="w-full min-h-screen flex flex-col">
      {isPending ? (
        <NavBarSkeleton loading={isPending} user={null} />
      ) : (
          <NavBar user={user} />
      )}

      <main className="flex-1">
        <Content user={user} />
      </main>
      <Footer />
    </div>
  );
};

export default DashboardPage;
