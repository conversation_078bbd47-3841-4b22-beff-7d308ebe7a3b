import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// PATCH /api/admin/lay-buy-orders/[id]/notes - Update admin notes
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { adminNotes } = body;

    const order = await prisma.layBuyOrder.update({
      where: { id: paramsId },
      data: {
        adminNotes: adminNotes || null,
        updatedAt: new Date(),
      },
    });

    const response: ApiResponse<typeof order> = {
      success: true,
      data: order,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating admin notes:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update admin notes" },
      { status: 500 }
    );
  }
}
