"use client";
import { useEffect } from "react";

export default function PrimaryColorClient() {
  useEffect(() => {
    async function applyPrimaryColor() {
      try {
        const res = await fetch("/api/admin/settings");
        const result = await res.json();
        if (result.success && result.data && result.data.primaryColor) {
          document.documentElement.style.setProperty('--primary', result.data.primaryColor);
        }
      } catch (e) {
        // Fallback: do nothing
      }
    }
    applyPrimaryColor();
  }, []);
  return null;
} 