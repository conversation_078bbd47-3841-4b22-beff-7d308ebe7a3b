import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import { getLayBuyOrderById } from "@/actions/layBuyActions";
import { sendLayBuyReminder } from "@/lib/email-service";
import { calculateReminderWeek, getReminderUrgency } from "@/lib/lay-buy-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
};

// POST /api/admin/lay-buy-orders/[id]/send-reminder - Manually send reminder
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { reminderType, force = false } = body;

    // Validate reminder type
    const validTypes = ['WEEKLY', 'URGENT', 'GRACE_PERIOD', 'FINAL_NOTICE'];
    if (reminderType && !validTypes.includes(reminderType)) {
      return NextResponse.json(
        { success: false, error: "Invalid reminder type" },
        { status: 400 }
      );
    }

    // Get the order
    const orderResult = await getLayBuyOrderById(paramsId);
    if (!orderResult.success || !orderResult.data) {
      return NextResponse.json(
        { success: false, error: "Order not found" },
        { status: 404 }
      );
    }

    const order = orderResult.data;

    // Check if order is active
    if (order.status !== 'ACTIVE') {
      return NextResponse.json(
        { success: false, error: "Can only send reminders for active orders" },
        { status: 400 }
      );
    }

    // Calculate appropriate reminder type if not specified
    let finalReminderType = reminderType;
    let weekNumber = 1;

    if (!reminderType) {
      const now = new Date();
      const createdAt = new Date(order.createdAt);
      weekNumber = calculateReminderWeek(createdAt, now);
      const { type } = getReminderUrgency(weekNumber);
      finalReminderType = type;
    } else {
      // If reminder type is specified, calculate week number based on type
      switch (reminderType) {
        case 'WEEKLY':
          weekNumber = Math.min(5, Math.max(1, Math.floor((Date.now() - new Date(order.createdAt).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1));
          break;
        case 'URGENT':
          weekNumber = 6;
          break;
        case 'GRACE_PERIOD':
        case 'FINAL_NOTICE':
          weekNumber = 7;
          break;
      }
    }

    // Check if a reminder was sent recently (unless forced)
    if (!force) {
      const recentReminder = await prisma.layBuyReminder.findFirst({
        where: {
          layBuyOrderId: order.id,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      if (recentReminder) {
        return NextResponse.json(
          { success: false, error: "A reminder was already sent in the last 24 hours. Use force=true to override." },
          { status: 400 }
        );
      }
    }

    // Send the reminder
    const emailResult = await sendLayBuyReminder(order, finalReminderType, weekNumber);

    if (!emailResult.success) {
      return NextResponse.json(
        { success: false, error: emailResult.error || "Failed to send reminder" },
        { status: 500 }
      );
    }

    // Record the reminder
    const reminder = await prisma.layBuyReminder.create({
      data: {
        layBuyOrderId: order.id,
        weekNumber,
        reminderType: finalReminderType,
        emailSent: true,
        smsSent: false,
      },
    });

    const response: ApiResponse<{
      reminder: typeof reminder;
      emailResult: typeof emailResult;
    }> = {
      success: true,
      data: {
        reminder,
        emailResult,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error sending manual reminder:", error);
    return NextResponse.json(
      { success: false, error: "Failed to send reminder" },
      { status: 500 }
    );
  }
}
