"use client";

import { useSession } from "@/lib/auth-client";
import { UserRoute } from "@/components/auth/protected-route";
import NavBar from "@/components/navbar";
import LayBuyOrderDetailContent from "@/components/lay-buy/lay-buy-order-detail-content";
import { useParams } from "next/navigation";
import { User } from "@/utils/types";
import NavBarSkeleton from "@/components/navBarSkeleton";

export default function LayBuyOrderDetailPage() {
  return (
    <UserRoute>
      <LayBuyOrderDetailPageContent />
    </UserRoute>
  );
}

function LayBuyOrderDetailPageContent() {
  const { data: session, isPending } = useSession();
  const params = useParams();
  const orderId = params.id as string;

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = session!.user as User;

  return (
    <div className="min-h-screen bg-gray-50">
      {isPending ? (
        <NavBarSkeleton loading={isPending} user={null} />
      ) : (
        <NavBar user={user} />
      )}
      <div className="container mx-auto px-4 py-6">
        <LayBuyOrderDetailContent orderId={orderId} />
      </div>
    </div>
  );
}
