import { Metadata } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth-utils";
import BulkProductUploadContent from "@/components/admin/products/bulk-upload/bulk-upload-content";

export const metadata: Metadata = {
  title: "Bulk Product Upload | Admin",
  description: "Upload multiple products efficiently with bulk upload tools",
};

export default async function BulkProductUploadPage() {
  // Check authentication and admin access
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/signin");
  }

  if (user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <BulkProductUploadContent user={user} />
      </div>
    </div>
  );
}
