import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";

type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

// GET /api/admin/lay-buy-cancellation-requests - Get all cancellation requests
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const search = searchParams.get("search");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (status && status !== "ALL") {
      where.status = status;
    }

    if (search) {
      where.OR = [
        {
          layBuyOrder: {
            orderNumber: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          requestedBy: {
            name: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          requestedBy: {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
      ];
    }

    // Get total count
    const total = await prisma.layBuyCancellationRequest.count({ where });

    // Get requests
    const requests = await prisma.layBuyCancellationRequest.findMany({
      where,
      skip,
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        layBuyOrder: {
          select: {
            orderNumber: true,
            totalAmount: true,
            amountPaid: true,
            status: true,
            dueDate: true,
          },
        },
        requestedBy: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    const response: ApiResponse<typeof requests> = {
      success: true,
      data: requests,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching cancellation requests:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch cancellation requests" },
      { status: 500 }
    );
  }
}
