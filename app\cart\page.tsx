"use client";
import { Suspense } from "react";

import { useSession } from "@/lib/auth-client";
import { UserRoute } from "@/components/auth/protected-route";
import NavBar from "@/components/navbar";
import CartContent from "@/components/cart/cart-content";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import { User } from "@/utils/types";
import NavBarSkeleton from "@/components/navBarSkeleton";

export default function CartPage() {
  return (
    // <UserRoute>
    <CartPageContent />
    // </UserRoute>
  );
}

function CartPageContent() {
  const { data: session, isPending } = useSession();

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  return (
    <div className="min-h-screen bg-gray-50">
      {isPending || !session ? (
        <NavBarSkeleton loading={isPending} user={null} />
      ) : (
        <Suspense fallback={<SpinnerCircle4 />}>
          {" "}
          <NavBar user={session!.user as User} />
        </Suspense>
      )}
      <div className="container mx-auto px-4 py-6">
        <CartContent />
      </div>
    </div>
  );
}
