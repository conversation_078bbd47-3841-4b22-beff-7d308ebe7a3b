/**
 * Lay-Buy Order Actions
 * Server actions for managing Lay-Buy orders, payments, and reminders
 */

import prisma from "@/lib/prisma";
import { generateLayBuyOrderNumber, calculateLayBuyDates } from "@/lib/lay-buy-utils";

/**
 * Create a new Lay-Buy order
 */
export async function createLayBuyOrder(orderData: {
  userId: string;
  totalAmount: number;
  upfrontAmount: number;
  remainingAmount: number;
  discountAmount?: number;
  shippingAddress: string;
  phoneNumber: string;
  notes?: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
    size?: string;
    color?: string;
  }>;
}) {
  try {
    const orderNumber = generateLayBuyOrderNumber();
    const dates = calculateLayBuyDates();

    const layBuyOrder = await prisma.layBuyOrder.create({
      data: {
        userId: orderData.userId,
        orderNumber,
        status: "ACTIVE",
        totalAmount: orderData.totalAmount,
        upfrontAmount: orderData.upfrontAmount,
        remainingAmount: orderData.remainingAmount,
        amountPaid: orderData.upfrontAmount, // Upfront payment counts as paid
        dueDate: dates.dueDate,
        gracePeriodEnd: dates.gracePeriodEnd,
        shippingAddress: orderData.shippingAddress,
        phoneNumber: orderData.phoneNumber,
        notes: orderData.notes,
        orderItems: {
          create: orderData.items.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            size: item.size,
            color: item.color,
          })),
        },
      },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
      },
    });

    return { success: true, data: layBuyOrder };
  } catch (error) {
    console.error("Error creating Lay-Buy order:", error);
    return { success: false, error: "Failed to create Lay-Buy order" };
  }
}

/**
 * Get Lay-Buy orders for a user
 */
export async function getUserLayBuyOrders(userId: string) {
  try {
    const orders = await prisma.layBuyOrder.findMany({
      where: { userId },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        payments: {
          orderBy: { createdAt: "desc" },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    return { success: true, data: orders };
  } catch (error) {
    console.error("Error fetching user Lay-Buy orders:", error);
    return { success: false, error: "Failed to fetch Lay-Buy orders" };
  }
}

/**
 * Get a single Lay-Buy order by ID
 */
export async function getLayBuyOrderById(orderId: string) {
  try {
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        payments: {
          orderBy: { createdAt: "desc" },
        },
        reminders: {
          orderBy: { createdAt: "desc" },
        },
      },
    });

    if (!order) {
      return { success: false, error: "Lay-Buy order not found" };
    }

    return { success: true, data: order };
  } catch (error) {
    console.error("Error fetching Lay-Buy order:", error);
    return { success: false, error: "Failed to fetch Lay-Buy order" };
  }
}

/**
 * Get all Lay-Buy orders for admin dashboard
 */
export async function getAllLayBuyOrders(filters?: {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}) {
  try {
    const { status, search, page = 1, limit = 20 } = filters || {};
    const skip = (page - 1) * limit;

    const where: any = {};
    
    if (status && status !== "ALL") {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: "insensitive" } },
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } },
      ];
    }

    const [orders, total] = await Promise.all([
      prisma.layBuyOrder.findMany({
        where,
        include: {
          user: true,
          orderItems: {
            include: {
              product: true,
            },
          },
          _count: {
            select: {
              payments: true,
              reminders: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.layBuyOrder.count({ where }),
    ]);

    return {
      success: true,
      data: {
        orders,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  } catch (error) {
    console.error("Error fetching all Lay-Buy orders:", error);
    return { success: false, error: "Failed to fetch Lay-Buy orders" };
  }
}

/**
 * Update Lay-Buy order status
 */
export async function updateLayBuyOrderStatus(
  orderId: string,
  status: "ACTIVE" | "COMPLETED" | "CANCELLED" | "FORFEITED" | "REFUNDED",
  adminNotes?: string
) {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    // Set appropriate timestamp based on status
    if (status === "COMPLETED") {
      updateData.completedAt = new Date();
    } else if (status === "CANCELLED") {
      updateData.cancelledAt = new Date();
    } else if (status === "FORFEITED") {
      updateData.forfeitedAt = new Date();
    }

    const order = await prisma.layBuyOrder.update({
      where: { id: orderId },
      data: updateData,
      include: {
        user: true,
        orderItems: {
          include: {
            product: true,
          },
        },
      },
    });

    return { success: true, data: order };
  } catch (error) {
    console.error("Error updating Lay-Buy order status:", error);
    return { success: false, error: "Failed to update Lay-Buy order status" };
  }
}

/**
 * Add payment to Lay-Buy order
 */
export async function addLayBuyPayment(paymentData: {
  layBuyOrderId: string;
  amount: number;
  paymentType: "UPFRONT" | "INSTALLMENT" | "COMPLETION";
  paymentMethod?: string;
  paymentProof?: string;
  notes?: string;
}) {
  try {
    const payment = await prisma.layBuyPayment.create({
      data: {
        layBuyOrderId: paymentData.layBuyOrderId,
        amount: paymentData.amount,
        paymentType: paymentData.paymentType,
        paymentMethod: paymentData.paymentMethod,
        paymentProof: paymentData.paymentProof,
        notes: paymentData.notes,
        status: "PENDING",
      },
    });

    return { success: true, data: payment };
  } catch (error) {
    console.error("Error adding Lay-Buy payment:", error);
    return { success: false, error: "Failed to add payment" };
  }
}
