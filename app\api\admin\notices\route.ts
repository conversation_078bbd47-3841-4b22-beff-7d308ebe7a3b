import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth-utils";
import { NoticeType } from "@/utils/types";

// GET /api/admin/notices - fetch all notices for admin management
export async function GET(req: NextRequest) {
  const user = await getCurrentUser();
  if (!user || user.role !== "ADMIN") {
    return NextResponse.json({ success: false, error: "Not authorized" }, { status: 403 });
  }

  const notices = await prisma.notice.findMany({
    orderBy: { createdAt: "desc" },
  });

  return NextResponse.json({ success: true, data: notices });
}

// POST /api/admin/notices - create a new notice (admin only)
export async function POST(req: NextRequest) {
  const user = await getCurrentUser();
  if (!user || user.role !== "ADMIN") {
    return NextResponse.json({ success: false, error: "Not authorized" }, { status: 403 });
  }
  const { title, content, type = "INFO", priority = 1, isActive = true } = await req.json();
  if (!title || !content) {
    return NextResponse.json({ success: false, error: "Missing title or content" }, { status: 400 });
  }
  const notice = await prisma.notice.create({
    data: {
      title,
      content,
      type,
      priority,
      isActive,
    },
  });
  return NextResponse.json({ success: true, data: notice });
}

// DELETE /api/admin/notices - delete a notice (admin only)
export async function DELETE(req: NextRequest) {
  const user = await getCurrentUser();
  if (!user || user.role !== "ADMIN") {
    return NextResponse.json({ success: false, error: "Not authorized" }, { status: 403 });
  }
  
  const { searchParams } = new URL(req.url);
  const id = searchParams.get("id");
  
  if (!id) {
    return NextResponse.json({ success: false, error: "Missing notice ID" }, { status: 400 });
  }

  await prisma.notice.delete({
    where: { id },
  });

  return NextResponse.json({ success: true });
} 