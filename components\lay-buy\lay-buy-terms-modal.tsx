"use client";

import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

import { Separator } from "@/components/ui/separator";
import { AlertTriangle, Clock, CreditCard, RefreshCw, XCircle } from "lucide-react";

interface LayBuyTermsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  totalAmount: number;
  upfrontAmount: number;
}

export default function LayBuyTermsModal({
  isOpen,
  onClose,
  onAccept,
  totalAmount,
  upfrontAmount,
}: LayBuyTermsModalProps) {
  const [hasAgreed, setHasAgreed] = useState(false);

  const handleAccept = () => {
    if (hasAgreed) {
      onAccept();
      setHasAgreed(false); // Reset for next time
    }
  };

  const handleClose = () => {
    setHasAgreed(false);
    onClose();
  };

  const remainingAmount = totalAmount - upfrontAmount;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-blue-600" />
            Lay-Buy Terms & Conditions
          </DialogTitle>
          <DialogDescription>
            Please read and agree to the following terms before proceeding with your Lay-Buy order.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[50vh] overflow-y-auto pr-4">
          <div className="space-y-6">
            {/* Order Summary */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h3 className="font-semibold text-blue-900 mb-2">Your Lay-Buy Order Summary</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Total Order Value:</span>
                  <span className="font-medium">M{totalAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Upfront Payment (60%):</span>
                  <span className="font-medium text-green-600">M{upfrontAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Remaining Balance (40%):</span>
                  <span className="font-medium text-orange-600">M{remainingAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Payment Period:</span>
                  <span className="font-medium">6 weeks + 1 week grace period</span>
                </div>
              </div>
            </div>

            {/* Official Terms & Conditions */}
            <div className="space-y-4">
              <div className="text-center border-b pb-3">
                <h3 className="text-lg font-bold text-gray-900">RIVV LAY-BUY TERMS & CONDITIONS</h3>
                <p className="text-sm text-gray-600">Effective Date: January 2025</p>
              </div>

              <p className="text-sm text-gray-700">
                These Terms and Conditions govern the Lay-Buy agreement between Rivv (referred to as "we", "us", or "our") and the customer (referred to as "you" or "the customer"). By choosing the Lay-Buy option, you agree to the following terms:
              </p>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">1. Lay-Buy Amount</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• A 60% upfront payment of the total price of the selected product is required to activate your lay-buy.</li>
                    <li>• The remaining 40% must be paid within 6 weeks from the date of order placement.</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">2. Payment Period</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• The standard lay-buy term is 6 weeks, starting from the date the initial 60% payment is received.</li>
                    <li>• If full payment is not made within 6 weeks, but you still wish to purchase the item, you will be granted a one-week grace period (Week 7).</li>
                    <li>• If payment is still not made after the grace period, you will forfeit all payments made, and the product will be released back into inventory.</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">3. Cancellation & Refunds</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• If you decide to cancel your lay-buy before the 6-week term ends, you will be refunded 50% of the total amount you've paid.</li>
                    <li>• The remaining 50% will be retained to cover holding and administrative costs.</li>
                    <li>• Cancellations requested after 6 weeks (plus the 1-week grace period) will not be eligible for any refund.</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">4. Ownership & Delivery</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• The product remains the property of Rivv until the full balance is paid.</li>
                    <li>• Your order will only be prepared for delivery once full payment has been received.</li>
                    <li>• You will be notified when your order is ready for delivery.</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">5. Reminders & Communication</h4>
                  <ul className="text-sm text-gray-600 space-y-1 ml-4">
                    <li>• You will receive weekly email reminders during the lay-buy period, informing you of your remaining balance and payment timeline.</li>
                    <li>• It is your responsibility to ensure your contact information (especially email) is correct and active.</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">6. Non-Transferable</h4>
                  <p className="text-sm text-gray-600 ml-4">• Lay-buys are non-transferable. You cannot assign or sell your lay-buy agreement to another person.</p>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">7. Amendments</h4>
                  <p className="text-sm text-gray-600 ml-4">• Rivv reserves the right to update or amend these terms at any time. Customers will be notified of any changes that may affect their existing lay-buy agreements.</p>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-900 mb-2">Need Help?</h4>
                  <p className="text-sm text-blue-800 mb-2">If you have any questions or concerns regarding your lay-buy, please contact us at:</p>
                  <div className="text-sm text-blue-700 space-y-1">
                    <p>📧 <EMAIL></p>
                    <p>📞 +266 62844473</p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Agreement Section */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Agreement</h4>
              <p className="text-sm text-gray-600 mb-4">
                By initiating a lay-buy with Rivv, you confirm that you have read, understood, and agreed to these Terms and Conditions.
              </p>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="agree-terms"
                  checked={hasAgreed}
                  onCheckedChange={(checked) => setHasAgreed(checked as boolean)}
                />
                <label
                  htmlFor="agree-terms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I have read and agree to the RIVV Lay-Buy Terms & Conditions
                </label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleAccept} 
            disabled={!hasAgreed}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Accept & Continue with Lay-Buy
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
